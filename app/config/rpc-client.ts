import { z } from 'zod'
import { isString } from '@kdt310722/utils/string'
import { httpUrl } from '../utils/schemas/urls'
import { retry } from '../utils/schemas/retry'

const schema = z.object({
    url: httpUrl,
    timeout: z.number().int().nonnegative().default(30_000),
    retry,
    headers: z.record(z.string(), z.string()).default({}),
    maxRequestPerSecond: z.number().min(1).int().default(100),
})

export const rpcClient = z.union([httpUrl, schema]).transform((val) => {
    return isString(val) ? schema.parse({ url: val }) : val
})
