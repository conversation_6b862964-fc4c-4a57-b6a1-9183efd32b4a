import { schemas } from '@kdt-sol/geyser-client'
import { z } from 'zod'
import { isString } from '@kdt310722/utils/string'
import { httpUrl } from '../utils/schemas/urls'

const schema = schemas.options.extend({
    url: httpUrl,
})

export type GeyserClientConfig = z.infer<typeof schema>

export const geyser = z.union([httpUrl, schema]).transform((val) => {
    return isString(val) ? schema.parse({ url: val }) : val
})
