import { isString, trim } from '@kdt310722/utils/string'
import { z } from 'zod'
import { bool } from '../utils/schemas/bool'

const levels = ['query', 'schema', 'error', 'warn', 'info', 'log', 'migration'] as const
const logging = z.preprocess((value) => (isString(value) ? (value === 'all' ? levels : value.split(',').map((i) => trim(i))) : value), z.enum(levels).array())

const batch = z.object({
    maxTime: z.coerce.number().int().nonnegative().default(50),
    maxSize: z.coerce.number().int().positive().default(50),
})

export const database = z.object({
    host: z.string().default('localhost'),
    port: z.coerce.number().int().positive().default(5432),
    username: z.string().default('postgres'),
    password: z.string().default('postgres'),
    name: z.string(),
    schema: z.string().default('public'),
    logging: logging.default(['error', 'warn', 'migration']),
    maxQueryExecutionTime: z.coerce.number().int().positive().default(1000),
    dropSchema: bool.default(false),
    runMigrations: bool.default(true),
    defaultChunkSize: z.coerce.number().int().positive().default(50),
    poolSize: z.coerce.number().int().positive().default(100),
    batch: batch.default({}),
    maxUpsertConcurrency: z.coerce.number().int().positive().default(10),
    cacheCandlestick: bool.default(true),
    showSaveTokenQueries: bool.default(false),
    showSaveTradeQueries: bool.default(false),
})
