import { z } from 'zod'
import { isString } from '@kdt310722/utils/string'
import { wsUrl } from '../utils/schemas/urls'
import { geyser } from './geyser'

const reconnect = z.object({
    enable: z.boolean().default(true),
    delay: z.number().int().nonnegative().default(1000),
    attempts: z.number().int().positive().default(5),
})

const timeout = z.object({
    connect: z.number().int().positive().default(10 * 1000),
    disconnect: z.number().int().positive().default(10 * 1000),
    request: z.number().int().positive().default(10 * 1000),
})

const rpcSchema = z.object({
    url: wsUrl,
    reconnect: reconnect.default({}),
    timeout: timeout.default({}),
})

export const rpc = z.union([wsUrl, rpcSchema]).transform((val) => {
    return isString(val) ? rpcSchema.parse({ url: val }) : val
})

export const datasource = z.discriminatedUnion('type', [
    z.object({ type: z.literal('geyser'), geyser }),
    z.object({ type: z.literal('rpc'), rpc }),
])
