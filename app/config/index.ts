import z from 'zod'
import { createConfig } from '../utils/config'
import { cleaner } from './cleaner'
import { database } from './database'
import { datasource } from './datasource'
import { indexer } from './indexer'
import { logger } from './logger'
import { rpcClient } from './rpc-client'
import { rpcServer } from './rpc-server'

const ignoreBlockNotFound = z.boolean().default(false)

export const config = createConfig({
    logger,
    database,
    rpcClient,
    backupRpcClient: rpcClient.nullish(),
    rpcServer,
    datasource,
    indexer,
    cleaner,
    ignoreBlockNotFound,
})
