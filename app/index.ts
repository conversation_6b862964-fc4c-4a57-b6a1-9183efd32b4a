import { tap } from '@kdt310722/utils/function'
import { waterfall } from '@kdt310722/utils/promise'
import { logger } from './common/logger'
import { initializeDatabase } from './common/database'
import { initializeRpcClient } from './common/rpc-client'
import { initializeDatasource } from './common/datasource'
import { initializeSlotIndexer } from './common/slot-indexer'
import { initializePumpFunIndexer } from './common/pumpfun-indexer'
import { initializePumpFunModule } from './common/pumpfun'
import { initializeGapFill, startGapFill } from './common/gap'
import { initializeRpcServer, startRpcServer } from './common/rpc-server'
import { initializeCleaner } from './common/cleaner'
import { initializeSolPrice } from './common/sol-price'

const initTimer = tap(logger.createTimer(), () => logger.info('Initializing application...'))
const tasks = waterfall([initializeRpcServer, initializeDatabase, initializeRpc<PERSON>lient, initializeDatasource, initializeSolPrice, initializeGapFill, initializeSlotIndexer, initializePumpFunIndexer, initializePumpFunModule])

const app = tasks.then(() => logger.stopTimer(initTimer, 'info', 'Application initialized!')).then(() => true).catch((error) => tap(false, () => {
    logger.exit(1, 'fatal', 'Failed to initialize application', error)
}))

app.then(async (initialized) => {
    if (initialized) {
        await startGapFill()
        await startRpcServer()
        await initializeCleaner()
    }
})
