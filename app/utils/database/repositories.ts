import { chunk } from '@kdt310722/utils/array'
import type { InsertResult, ObjectLiteral, Repository } from 'typeorm'
import { notNullish } from '@kdt310722/utils/common'
import { config } from '../../config'

export enum ConflictType {
    IGNORE = 'IGNORE',
    UPDATE = 'UPDATE',
}

export async function upsert<E extends ObjectLiteral>(repository: Repository<E>, data: E | E[], conflictPaths: Array<Extract<keyof E, string> | string>, conflictType: ConflictType = ConflictType.UPDATE, comment?: string) {
    const query = repository.createQueryBuilder().insert().into(repository.target).values(data).returning(conflictPaths)

    if (notNullish(comment)) {
        query.comment(comment)
    }

    if (conflictType === ConflictType.UPDATE) {
        query.orUpdate(conflictPaths, conflictPaths, { upsertType: 'on-conflict-do-update' })
    } else {
        query.orIgnore()
    }

    return query.execute()
}

export async function chunkUpsert<E extends ObjectLiteral>(repository: Repository<E>, dataset: E[], conflictPaths: Array<Extract<keyof E, string> | string>, conflictType: ConflictType = ConflictType.UPDATE, chunkSize = config.database.defaultChunkSize, comment?: string) {
    const result: InsertResult[] = []
    const chunks = chunk(dataset, chunkSize)

    for (const data of chunks) {
        result.push(await upsert(repository, data, conflictPaths, conflictType, comment))
    }

    return result
}

export async function chunkInsert<E extends ObjectLiteral>(repository: Repository<E>, dataset: E[], chunkSize = config.database.defaultChunkSize) {
    const result: InsertResult[] = []
    const chunks = chunk(dataset, chunkSize)

    for (const data of chunks) {
        result.push(await repository.insert(data))
    }

    return result
}
