import { LogLevel, type Logger } from '@kdt310722/logger'
import type { LogLevel as DatabaseLogLevel, LogMessage, LoggerOptions } from 'typeorm'
import { AbstractLogger } from 'typeorm'
import { config } from '../../config'

export const levels = <const>{
    'query': LogLevel.DEBUG,
    'query-error': LogLevel.ERROR,
    'query-slow': LogLevel.WARN,
    'schema': LogLevel.INFO,
    'schema-build': LogLevel.DEBUG,
    'error': LogLevel.ERROR,
    'warn': LogLevel.WARN,
    'info': LogLevel.DEBUG,
    'log': LogLevel.INFO,
    'migration': LogLevel.INFO,
}

export class DatabaseLogger extends AbstractLogger {
    public constructor(protected readonly logger: Logger, options?: LoggerOptions) {
        super(options)
    }

    protected override writeLog(level: DatabaseLogLevel, message: LogMessage | string | number) {
        const messages = this.prepareLogMessages(message)

        for (const msg of messages) {
            const message = msg.message.toString()
            const logType = msg.type ?? level
            const logLevel = levels[logType] ?? LogLevel.INFO
            const logTypeStr = logType.toUpperCase()

            if (logLevel === LogLevel.DEBUG) {
                if (!config.database.showSaveTokenQueries && message.includes('/* save_token_query */')) {
                    continue
                }

                if (!config.database.showSaveTradeQueries && message.includes('/* save_trade_query */')) {
                    continue
                }
            }

            this.logger.log(logLevel, `[${logTypeStr}] ${msg.prefix?.length ? `${msg.prefix} ` : ''}${message}`)
        }
    }
}
