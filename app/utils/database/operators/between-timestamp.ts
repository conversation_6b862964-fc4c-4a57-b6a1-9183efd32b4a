import { type Nullable, notNullish } from '@kdt310722/utils/common'
import { Raw } from 'typeorm'

export const BetweenTimestamp = (start?: Nullable<number>, end?: Nullable<number>) => {
    const hasStart = notNullish(start)
    const hasEnd = notNullish(end)

    if (!hasStart && !hasEnd) {
        return Raw(() => 'TRUE')
    }

    const startQuery = (alias: string) => `EXTRACT(EPOCH FROM ${alias}) >= :start`
    const endQuery = (alias: string) => `EXTRACT(EPOCH FROM ${alias}) <= :end`

    if (hasStart && !hasEnd) {
        return Raw(startQuery, { start })
    }

    if (!hasStart && hasEnd) {
        return Raw(endQuery, { end })
    }

    return Raw((alias) => `${startQuery(alias)} AND ${endQuery(alias)}`, { start, end })
}
