import { type Nullable, notNullish } from '@kdt310722/utils/common'
import { LessThanOrEqual, MoreThanOrEqual, Raw } from 'typeorm'

export const BetweenRange = (start?: Nullable<number | bigint>, end?: Nullable<number | bigint>) => {
    const hasStart = notNullish(start)
    const hasEnd = notNullish(end)

    if (!hasStart && !hasEnd) {
        return Raw(() => 'TRUE')
    }

    if (hasStart && !hasEnd) {
        return MoreThanOrEqual(start)
    }

    if (!hasStart && hasEnd) {
        return LessThanOrEqual(end)
    }

    return Raw((alias) => `${alias} >= '${start}' AND ${alias} <= '${end}'`)
}
