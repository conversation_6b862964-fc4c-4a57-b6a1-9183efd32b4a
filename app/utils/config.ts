import type { ZodTypeAny } from 'zod'
import { defineConfig } from '@kdt310722/config'
import { printConfigError } from './cli'

export function createConfig<S extends Record<string, ZodTypeAny>>(schema: S) {
    const config = defineConfig(schema, {
        search: { name: 'config' },
        formatError: false,
    })

    try {
        return config.parse()
    } catch (error) {
        return printConfigError(error)
    }
}
