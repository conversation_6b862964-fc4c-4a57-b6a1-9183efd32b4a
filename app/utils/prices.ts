import { calculateTokenPrice } from '@kdt-sol/pumpfun-sdk'

export function getPumpFunTokenPrice(virtualSolReserves: bigint, virtualTokenReserves: bigint) {
    return calculateTokenPrice(virtualSolReserves, virtualTokenReserves, 9n)
}

export function getPumpFunVirtualReservesBefore(solReserves: bigint, tokenReserves: bigint, solAmount: bigint, tokenAmount: bigint, isBuy: boolean) {
    const virtualSolReserves = isBuy ? solReserves - solAmount : solReserves + solAmount
    const virtualTokenReserves = isBuy ? tokenReserves + tokenAmount : tokenReserves - tokenAmount

    return { virtualSolReserves, virtualTokenReserves }
}

export function getPumpFunTokenPriceBefore(virtualSolReserves: bigint, virtualTokenReserves: bigint, solAmount: bigint, tokenAmount: bigint, isBuy: boolean) {
    const reserves = getPumpFunVirtualReservesBefore(virtualSolReserves, virtualTokenReserves, solAmount, tokenAmount, isBuy)
    const { virtualSolReserves: virtualSolReservesBefore, virtualTokenReserves: virtualTokenReservesBefore } = reserves

    return getPumpFunTokenPrice(virtualSolReservesBefore, virtualTokenReservesBefore)
}
