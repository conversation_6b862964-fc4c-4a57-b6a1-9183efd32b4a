import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'

export const rootPath = (...paths: string[]) => resolve(dirname(fileURLToPath(import.meta.url)), '..', '..', ...paths)

export const appPath = (...paths: string[]) => rootPath('app', ...paths)

export const storagePath = (...paths: string[]) => rootPath('storage', ...paths)

export const logsPath = (...paths: string[]) => storagePath('logs', ...paths)

export const routesPath = (...paths: string[]) => appPath('routes', ...paths)
