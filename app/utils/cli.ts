import { EOL } from 'node:os'
import { ParseConfigError, isZodError } from '@kdt310722/config'
import { fromZodError } from 'zod-validation-error'
import { badge, text } from '@kdt310722/logger'

export function printConfigError(error: unknown): never {
    if (error instanceof ParseConfigError && isZodError(error.cause)) {
        const err = fromZodError(error.cause, {
            maxIssuesInMessage: Number.POSITIVE_INFINITY,
            issueSeparator: '\n  + ',
            prefix: 'Invalid config:\n  + ',
            prefixSeparator: '',
            unionSeparator: '\nor ',
        })

        process.stderr.write(`${badge('Error')} ${text(err.message)}${EOL}`)
        process.exit(1)
    }

    throw error
}
