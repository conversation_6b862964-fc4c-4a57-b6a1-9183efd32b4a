import { pick } from '@kdt310722/utils/object'
import type { PumpFunTrade } from '../../modules/indexer/entities'
import { Trade } from '../../entities/trade'
import { getPumpFunTokenPrice, getPumpFunTokenPriceBefore } from '../prices'
import { getSortKey } from '../keys'

export const toTradeEntity = (trade: PumpFunTrade): Trade => {
    const entity = new Trade()

    entity.user = trade.user
    entity.mint = trade.mint
    entity.isBuy = trade.isBuy
    entity.solAmount = trade.solAmount
    entity.tokenAmount = trade.tokenAmount
    entity.virtualSolReserves = trade.virtualSolReserves
    entity.virtualTokenReserves = trade.virtualTokenReserves
    entity.realSolReserves = trade.realSolReserves
    entity.realTokenReserves = trade.realTokenReserves
    entity.tokenPriceBefore = getPumpFunTokenPriceBefore(trade.virtualSolReserves, trade.virtualTokenReserves, trade.solAmount, trade.tokenAmount, trade.isBuy)
    entity.tokenPrice = getPumpFunTokenPrice(trade.virtualSolReserves, trade.virtualTokenReserves)
    entity.slot = trade.slot
    entity.sortKey = getSortKey(trade.slot, trade.transactionIndex, trade.eventIndex)
    entity.signature = trade.signature
    entity.transactionIndex = trade.transactionIndex
    entity.eventIndex = trade.eventIndex
    entity.timestamp = new Date(trade.timestamp * 1000)
    entity.metadata = pick(trade, 'feeRecipient', 'feeBasisPoints', 'fee', 'creator', 'creatorFeeBasisPoints', 'creatorFee')

    return entity
}
