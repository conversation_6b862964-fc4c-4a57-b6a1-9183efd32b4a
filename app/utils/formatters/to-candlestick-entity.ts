import type { ObjectLiteral } from 'typeorm'
import type { Candlestick } from '../../entities/candlestick'

export const toCandlestickEntity = (input: ObjectLiteral): Candlestick => ({
    mint: input.mint,
    timestamp: input.timestamp,
    open: BigInt(input.open),
    high: BigInt(input.high),
    low: BigInt(input.low),
    close: BigInt(input.close),
    volumeSol: BigInt(input.volumeSol),
    volumeToken: BigInt(input.volumeToken),
    tradesCount: Number(input.tradesCount),
})
