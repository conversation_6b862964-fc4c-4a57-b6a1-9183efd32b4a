import type { PumpFunGlobalAccountUpdateEvent } from '../../modules/indexer/entities'
import { GlobalAccount } from '../../entities/global-account'

export const toGlobalAccountEntity = (account: PumpFunGlobalAccountUpdateEvent): GlobalAccount => {
    const entity = new GlobalAccount()

    entity.feeRecipient = account.feeRecipients[0]
    entity.initialVirtualTokenReserves = account.initialVirtualTokenReserves
    entity.initialVirtualSolReserves = account.initialVirtualTokenReserves
    entity.initialRealTokenReserves = account.initialRealTokenReserves
    entity.tokenTotalSupply = account.tokenTotalSupply
    entity.feeBasisPoints = account.feeBasisPoints
    entity.creatorFeeBasisPoints = account.creatorFeeBasisPoints

    return entity
}
