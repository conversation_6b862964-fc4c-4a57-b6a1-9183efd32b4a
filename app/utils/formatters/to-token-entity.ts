import type { <PERSON>umpFunToken } from '../../modules/indexer/entities'
import { Token } from '../../entities/token'

export const toTokenEntity = (token: Omit<PumpFunToken, 'timestamp'> & { timestamp: number, isCompleted?: boolean }): Token => {
    const entity = new Token()

    entity.mint = token.mint
    entity.name = token.name
    entity.symbol = token.symbol
    entity.decimals = 6
    entity.metadataUri = token.uri
    entity.isCompleted = token.isCompleted ?? false
    entity.createdBy = token.user
    entity.createdAtSlot = token.slot
    entity.createdAtTransaction = token.signature
    entity.createdAt = new Date(token.timestamp * 1000)

    return entity
}
