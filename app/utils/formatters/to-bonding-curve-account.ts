import { SYSTEM_PROGRAM_ADDRESS } from '@kdt-sol/pumpfun-sdk'
import type { Trade } from '../../entities/trade'
import type { BondingCurveAccount } from '../../entities/bonding-curve-account'

export const toBondingCurveAccount = (trade: Pick<Trade, 'slot' | 'sortKey' | 'transactionIndex' | 'signature' | 'eventIndex' | 'virtualTokenReserves' | 'virtualSolReserves' | 'realTokenReserves' | 'realSolReserves' | 'metadata'>): BondingCurveAccount => ({
    slot: trade.slot,
    sortKey: trade.sortKey,
    signature: trade.signature,
    transactionIndex: trade.transactionIndex,
    eventIndex: trade.eventIndex,
    virtualTokenReserves: trade.virtualTokenReserves,
    virtualSolReserves: trade.virtualSolReserves,
    realTokenReserves: trade.realTokenReserves,
    realSolReserves: trade.realSolReserves,
    creator: trade.metadata?.creator ?? SYSTEM_PROGRAM_ADDRESS,
    feeBasisPoints: trade.metadata?.feeBasisPoints,
    creatorFeeBasisPoints: trade.metadata?.creatorFeeBasisPoints,
})
