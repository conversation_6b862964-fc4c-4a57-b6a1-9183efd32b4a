import { highlight } from '@kdt310722/logger'
import { format, formatNanoseconds } from '@kdt310722/utils/number'
import { createDeferred } from '@kdt310722/utils/promise'
import { formatDate } from '@kdt310722/utils/time'
import { Cleaner } from '../modules/cleaner/cleaner'
import { config } from '../config'
import { createChildLogger } from './logger'

const logger = createChildLogger('common:cleaner')
const cleaner = new Cleaner({ ...config.cleaner, immediate: config.cleaner.runOnStart })

cleaner.on('error', (error) => {
    logger.error('Cleaner error', error)
})

cleaner.on('cleanTokens', (time, cleanCompleted) => {
    logger.info(`Cleaning tokens older than ${highlight(formatDate(time))}${cleanCompleted ? ' and completed tokens' : ''}...`)
})

cleaner.on('cleanedTokens', (total, took) => {
    logger.info(`Cleaned ${highlight(format(total))} tokens (took ${formatNanoseconds(took)})`)
})

cleaner.on('cleanTrades', () => {
    logger.info(`Cleaning trades not belong to any token...`)
})

cleaner.on('cleanedTrades', (total, took) => {
    logger.info(`Cleaned ${highlight(format(total))} trades (took ${formatNanoseconds(took)})`)
})

export async function initializeCleaner() {
    if (!config.cleaner.enabled) {
        return
    }

    const startTime = createDeferred<Date>()

    cleaner.on('startTime', (time) => {
        startTime.resolve(time)
    })

    await cleaner.start().then(() => startTime).then((time) => logger.info(`Cleaner will start at: ${highlight(formatDate(time))}`))
}
