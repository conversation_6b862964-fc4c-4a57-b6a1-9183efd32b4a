import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { type Commitment, createSolanaRpcFromTransport } from '@solana/kit'
import { config } from '../config'
import { createSolanaRpcTransport } from '../utils/rpc-client/transports'
import { toTransportResponse } from '../utils/rpc-client/utils'
import { createChildLogger } from './logger'

const logger = createChildLogger('clients:rpc')

const createTransport = (params: typeof config.rpcClient) => createSolanaRpcTransport({
    ...params,
    onRequest: (request) => tap(logger.createTimer(`rpc-client:request:${request.id}`), () => logger.debug('Rpc request', request)),
    onResponse: (body, response, request) => logger.stopTimer(`rpc-client:request:${request.id}`, 'debug', 'Rpc response', { request: request.id, response: toTransportResponse(body, response) }),
    retry: { ...params.retry, onFailedAttempt: (error) => logger.warn('Request failed, retrying...', error) },
})

const transport = createTransport(config.rpcClient)
const backupTransport = config.backupRpcClient ? createTransport(config.backupRpcClient) : undefined

export const rpcClient = createSolanaRpcFromTransport(transport)
export const backupRpcClient = backupTransport ? createSolanaRpcFromTransport(backupTransport) : undefined

async function checkConnection(name: string, client: typeof rpcClient) {
    const stop = logger.createLoading().start(`Checking ${name} RPC client connection...`)

    await client.getHealth().send().then(() => {
        stop(`${name} RPC client connection is healthy!`)
    })
}

export async function initializeRpcClient() {
    await checkConnection('Primary', rpcClient)

    if (notNullish(backupRpcClient)) {
        await checkConnection('Backup', backupRpcClient)
    }
}

export interface GetSlotsOptions {
    limit?: number
    commitment?: Exclude<Commitment, 'processed'>
}

export async function getSlots(start: number, end: number, { limit = 500_000, commitment = 'finalized' }: GetSlotsOptions = {}) {
    if (end < start) {
        throw Object.assign(new Error('End slot must be greater than start slot'), { start, end })
    }

    const slots: number[] = []

    for (let i = start; i <= end; i += limit) {
        slots.push(...(await rpcClient.getBlocks(BigInt(i), BigInt(Math.min(i + limit - 1, end)), { commitment }).send().then((slots) => slots.map(Number))))
    }

    return slots
}
