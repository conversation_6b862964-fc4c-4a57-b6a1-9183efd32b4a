import { tap } from '@kdt310722/utils/function'
import PQueue from 'p-queue'
import { type DeferredPromise, createDeferred } from '@kdt310722/utils/promise'
import { notNullish } from '@kdt310722/utils/common'
import { addExitHandler, isExiting } from '@kdt310722/logger'
import { Gap } from '../entities/gap'
import { GapFill } from '../modules/gap/gap-fill'
import { database } from './database'
import { onBeforeGap, onGap, onNoMissingBlocks } from './slot-indexer'
import { createChildLogger } from './logger'
import { pauseCompleteQueue, startCompleteQueue } from './pumpfun'

let gapsCount = 0

export const gapRepository = database.getRepository(Gap)
export const getGapId = (startSlot: number, endSlot: number) => `${startSlot}-${endSlot}`

const logger = createChildLogger('gap')
const queue = new PQueue({ concurrency: 1, autoStart: false })
const pendingGaps: Record<string, DeferredPromise<void>> = {}
const pendingSaveGaps: Record<string, Promise<Gap | null>> = {}

queue.on('add', () => pauseCompleteQueue())
queue.on('idle', () => startCompleteQueue())

addExitHandler(() => Promise.resolve().then(() => {
    gapsCount = 0
}))

onBeforeGap((startSlot, endSlot) => {
    const id = tap(getGapId(startSlot, endSlot), (id) => logger.debug(`Saving gap: ${id}`))

    pendingGaps[id] = createDeferred()
    Promise.resolve().then(() => queue.add(async () => pendingGaps[id]))

    pendingSaveGaps[id] = gapRepository.save(gapRepository.create({ startSlot, endSlot })).catch((error) => {
        return tap(null, () => logger.exit(1, 'fatal', `Failed to save gap: ${startSlot} - ${endSlot}`, error))
    })

    pendingGaps[id].finally(() => delete pendingGaps[id])
})

onNoMissingBlocks((startSlot, endSlot) => {
    const id = tap(getGapId(startSlot, endSlot), (id) => logger.debug(`Deleting unneeded gap: ${id}`))

    const promise = pendingSaveGaps[id]?.then((gap) => (notNullish(gap) ? gapRepository.remove(gap) : null)).catch((error) => {
        logger.exit(1, 'fatal', `Failed to delete unneeded gap: ${startSlot} - ${endSlot}`, error)
    })

    pendingGaps[id]?.resolve()
    promise?.then(() => delete pendingSaveGaps[id])
})

export const gapFill = new GapFill()

export function addGap(gap: Gap) {
    gapsCount++

    queue.add(async () => gapFill.fill(gap)).then(async () => gapRepository.save(tap(gap, () => (gap.isResolved = true)))).catch((error) => {
        logger.exit(1, 'fatal', `Failed to fill gap: ${gap.startSlot} - ${gap.endSlot}`, error)
    })
}

onGap((startSlot, endSlot, slots) => {
    const id = getGapId(startSlot, endSlot)

    if (!pendingSaveGaps[id]) {
        return logger.exit(1, 'fatal', `Gap not found: ${startSlot} - ${endSlot}`)
    }

    logger.debug(`Updating gap: ${id}`)

    const gap = pendingSaveGaps[id].then(async (gap) => (notNullish(gap) ? (await gapRepository.save(tap(gap, () => (gap.slots = slots)))) : null)).catch((error) => {
        return tap(null, () => logger.exit(1, 'fatal', `Failed to update gap: ${startSlot} - ${endSlot}`, error))
    })

    gap.then((gap) => notNullish(gap) && addGap(gap)).then(() => pendingGaps[id]?.resolve()).then(() => delete pendingSaveGaps[id])
})

export async function initializeGapFill() {
    const stop = logger.createLoading().start('Initializing gap fill...')
    const gaps = await gapRepository.find({ where: { isResolved: false } })

    for (const gap of gaps) {
        addGap(gap)
    }

    stop('Gap fill initialized!')
}

export async function startGapFill() {
    if (isExiting()) {
        return
    }

    queue.start().on('idle', () => {
        if (gapsCount > 0) {
            logger.info('All gaps are filled!')
            gapsCount = 0
        }
    })

    return queue.onIdle()
}
