import { createDeferred } from '@kdt310722/utils/promise'
import { tap } from '@kdt310722/utils/function'
import { notNullish } from '@kdt310722/utils/common'
import { LogLevel, isExiting } from '@kdt310722/logger'
import PQueue from 'p-queue'
import { config } from '../config'
import { getFirstKnownSlot, getLastKnownSlot, saveToken, saveTrade, tokenRepository } from './entities'
import { pumpfunIndexer } from './pumpfun-indexer'
import { createChildLogger } from './logger'
import { handleGap, slotIndexer } from './slot-indexer'
import { rpcClient } from './rpc-client'

const logger = createChildLogger('pumpfun')
const firstReceivedSlotPromise = createDeferred<number>()

let firstReceivedSlot: number | undefined
let firstReceivedTradeSlot: number | undefined

const updater = <const>{
    slot: (slot: number) => !firstReceivedSlot && (firstReceivedSlot = slot),
    trade: (slot: number) => !firstReceivedTradeSlot && (firstReceivedTradeSlot = slot),
}

function setFirstReceivedSlot(type: 'slot' | 'trade', slot: number) {
    if (!firstReceivedSlotPromise.isSettled) {
        updater[type](slot)

        if (firstReceivedSlot && firstReceivedTradeSlot) {
            firstReceivedSlotPromise.resolve(Math.max(firstReceivedSlot, firstReceivedTradeSlot))
        }
    }
}

export const completeQueue = new PQueue({ autoStart: false })

export function pauseCompleteQueue() {
    if (!completeQueue.isPaused) {
        completeQueue.pause()
        logger.debug('Complete queue paused')
    }
}

export function startCompleteQueue() {
    if (completeQueue.isPaused) {
        completeQueue.start()
        logger.debug('Complete queue started')
    }
}

function registerEvents() {
    slotIndexer.on('slot', ({ slot }) => {
        setFirstReceivedSlot('slot', slot)
    })

    pumpfunIndexer.on('token', (token) => {
        if (isExiting()) {
            return
        }

        saveToken(token).catch((error) => logger.exit(1, 'fatal', `Failed to save token ${token.mint}`, { signature: token.createdAtTransaction }, error))
    })

    pumpfunIndexer.on('complete', (mint) => {
        if (isExiting()) {
            return
        }

        completeQueue.add(async () => tokenRepository.update({ mint }, { isCompleted: true })).catch((error) => logger.exit(1, 'fatal', `Failed to mark token ${mint} as completed`, error))
    })

    pumpfunIndexer.on('trade', (trade) => {
        if (isExiting()) {
            return
        }

        setFirstReceivedSlot('trade', trade.slot)
        saveTrade(trade).catch((error) => logger.exit(1, 'fatal', 'Failed to save trade', { signature: trade.signature, index: trade.eventIndex }, error))
    })
}

async function getStartSlot(firstKnownSlot?: number) {
    const startSlot = config.indexer.startSlot

    if (!startSlot || startSlot >= 0) {
        return startSlot
    }

    if (notNullish(firstKnownSlot)) {
        return firstKnownSlot + startSlot
    }

    return rpcClient.getSlot({ commitment: 'finalized' }).send().then((slot) => Number(slot) + startSlot)
}

export async function initializePumpFunModule() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing PumpFun module...'))
    const [lastKnownSlot, firstKnownSlot] = await Promise.all([getLastKnownSlot(), getFirstKnownSlot()])
    const startSlot = await getStartSlot(firstKnownSlot)

    registerEvents()

    if (notNullish(startSlot)) {
        if (notNullish(firstKnownSlot) && startSlot > firstKnownSlot) {
            throw new Error(`Invalid start slot: ${startSlot} > ${firstKnownSlot}`)
        }

        await firstReceivedSlotPromise.then((slot) => handleGap(startSlot, firstKnownSlot ?? slot, LogLevel.INFO))
    }

    if (lastKnownSlot) {
        await firstReceivedSlotPromise.then((slot) => handleGap(lastKnownSlot, slot, LogLevel.INFO))
    }

    logger.stopTimer(timer, 'info', 'PumpFun module initialized!')
}
