import { highlight } from '@kdt310722/logger'
import { Emitter } from '@kdt310722/utils/event'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { formatDate, timestamp } from '@kdt310722/utils/time'
import { PumpFunIndexer } from '../modules/indexer/pumpfun'
import type { Token } from '../entities/token'
import { toTokenEntity } from '../utils/formatters/to-token-entity'
import type { Trade } from '../entities/trade'
import { toTradeEntity } from '../utils/formatters/to-trade-entity'
import type { GlobalAccount } from '../entities/global-account'
import { toGlobalAccountEntity } from '../utils/formatters/to-global-account-entity'
import { isParseLogsExpectedError } from '../utils/errors'
import { createChildLogger } from './logger'
import { datasource } from './datasource'
import { slotIndexer } from './slot-indexer'
import { rpcClient } from './rpc-client'

const logger = createChildLogger('indexers:pumpfun')
const indexer = new PumpFunIndexer(datasource)

indexer.on('unhandledEvent', (event, context) => logger.warn(`Unhandled PumpFun event`, { event, context }))
indexer.on('unhealthy', (reason) => logger.error(`PumpFun indexer is unhealthy: ${highlight(reason)}`))
indexer.on('error', (error) => logger.exit(1, 'fatal', 'PumpFun indexer error', error))

indexer.on('parseLogsError', (signature, error) => {
    if (isParseLogsExpectedError(error)) {
        logger.error(`Missing event identifier for logs from signature: ${signature}`)
    } else {
        logger.error(`Failed to parse logs from signature: ${signature}`, error)
    }
})

type PumpFunEvents = {
    token: (token: Token) => void
    trade: (trade: Trade) => void
    complete: (mint: string) => void
    globalAccount: (account: GlobalAccount) => void
}

export const pumpfunIndexer = new Emitter<PumpFunEvents, true>()

indexer.on('trade', (trade) => pumpfunIndexer.emit('trade', toTradeEntity(trade)))
indexer.on('complete', ({ mint }) => pumpfunIndexer.emit('complete', mint))
indexer.on('globalAccount', (account) => pumpfunIndexer.emit('globalAccount', toGlobalAccountEntity(account)))

const waiters: Record<number, Promise<number>> = {}

const getSlotTimestamp = (slot: number) => slotIndexer.waitForSlot(slot).then((slot) => slot.timestamp).then((timestamp) => {
    if (notNullish(timestamp)) {
        return timestamp
    }

    const timer = tap(logger.createTimer(), () => logger.debug(`Missing timestamp for slot ${highlight(slot)}, fetching from rpc...`))

    return rpcClient.getBlockTime(BigInt(slot)).send().then((timestamp) => tap(Number(timestamp), (i) => {
        logger.stopTimer(timer, 'debug', `Slot ${highlight(slot)} timestamp: ${highlight(formatDate(new Date(i * 1000)))}`)
    }))
})

const waitForSlotTimestamp = (slot: number) => waiters[slot] ??= getSlotTimestamp(slot).then((timestamp) => tap(timestamp, () => {
    delete waiters[slot]
}))

indexer.on('token', (token) => {
    const onWaitFail = (error: unknown) => {
        return tap(timestamp(), () => logger.error(`Failed to wait for block time for slot: ${token.slot}`, error))
    }

    Promise.resolve(isNullish(token.timestamp) ? waitForSlotTimestamp(token.slot).catch(onWaitFail) : token.timestamp).then((timestamp) => {
        pumpfunIndexer.emit('token', toTokenEntity({ ...token, timestamp }))
    })
})

export async function initializePumpFunIndexer() {
    const stop = logger.createLoading().start('Listening for new PumpFun events...')

    await indexer.start().then(() => {
        stop('PumpFun indexer started!')
    })
}
