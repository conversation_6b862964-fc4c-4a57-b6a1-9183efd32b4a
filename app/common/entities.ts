import { IsNull, Not, type ObjectLiteral, type Repository } from 'typeorm'
import { tap } from '@kdt310722/utils/function'
import { format } from '@kdt310722/utils/number'
import { addExitHandler, highlight, message } from '@kdt310722/logger'
import Bottleneck from 'bottleneck'
import PQueue from 'p-queue'
import { Token } from '../entities/token'
import { Trade } from '../entities/trade'
import { ConflictType, chunkUpsert } from '../utils/database/repositories'
import { config } from '../config'
import { database } from './database'
import { createChildLogger } from './logger'

export const tokenRepository = database.getRepository(Token)
export const tradeRepository = database.getRepository(Trade)

const logger = createChildLogger('entities')
const queue = new PQueue({ concurrency: config.database.maxUpsertConcurrency })

addExitHandler(async () => {
    if (queue.size > 0 || queue.pending > 0) {
        await Promise.resolve(logger.info('Waiting for the database queue to finish...')).then(async () => queue.onIdle())
    }
})

export async function _batchInsert<E extends ObjectLiteral>(name: string, repository: Repository<E>, entities: E[], conflictPaths: Array<Extract<keyof E, string> | string>, comment?: string) {
    const timer = logger.createTimer()
    const insertResults = await chunkUpsert(repository, entities, conflictPaths, ConflictType.UPDATE, undefined, comment)
    const insertedEntities: E[] = []

    for (const { generatedMaps } of insertResults) {
        for (const identifier of generatedMaps) {
            const keys = Object.keys(identifier)

            if (keys.length === 0) {
                continue
            }

            const entity = entities.find((e) => keys.every((key) => e[key] === identifier[key]))

            if (entity) {
                insertedEntities.push(entity)
            }
        }
    }

    return tap(insertedEntities, () => logger.stopTimer(timer, 'debug', message(() => `Inserted ${highlight(format(insertedEntities.length))} ${name} entities into the database`)))
}

export async function batchInsert<E extends ObjectLiteral>(name: string, repository: Repository<E>, entities: E[], conflictPaths: Array<Extract<keyof E, string> | string>, comment?: string) {
    return (await queue.add(() => _batchInsert(name, repository, entities, conflictPaths, comment))) as E[]
}

export function createBatcher<T extends ObjectLiteral>(name: string, insert: (entities: T[]) => Promise<T[]>) {
    const batcher = new Bottleneck.Batcher(config.database.batch)

    batcher.on('batch', (entities) => {
        insert(entities).catch((error) => logger.exit(1, 'fatal', `Failed to insert ${entities.length} ${name} entities into the database`, error))
    })

    batcher.on('error', (error) => {
        logger.exit(1, 'fatal', `An error occurred in the ${name} batcher`, error)
    })

    return batcher
}

export async function batchSaveTokens(tokens: Token[]) {
    return batchInsert('token', tokenRepository, tokens, ['mint'], 'save_token_query')
}

export async function batchSaveTrades(trades: Trade[]) {
    return batchInsert('trade', tradeRepository, trades, ['signature', 'event_index', 'timestamp'], 'save_trade_query')
}

const tokenBatcher = createBatcher<Token>('token', async (tokens) => batchSaveTokens(tokens))
const tradeBatcher = createBatcher<Trade>('trade', async (trades) => batchSaveTrades(trades))

export async function saveToken(token: Token) {
    return tokenBatcher.add(token)
}

export async function saveTrade(trade: Trade) {
    return tradeBatcher.add(trade)
}

export async function getKnownSlot(order: 'ASC' | 'DESC') {
    const [tokenSlot, tradeSlot] = await Promise.all([tokenRepository.findOne({ where: { createdAtSlot: Not(IsNull()) }, order: { createdAtSlot: order } }), tradeRepository.findOne({ where: { slot: Not(IsNull()) }, order: { slot: order } })])

    if (!tradeSlot) {
        return tokenSlot?.createdAtSlot
    }

    if (!tokenSlot) {
        return tradeSlot.slot
    }

    return Math[order === 'ASC' ? 'min' : 'max'](tokenSlot.createdAtSlot, tradeSlot.slot)
}

export async function getFirstKnownSlot() {
    return getKnownSlot('ASC')
}

export async function getLastKnownSlot() {
    return getKnownSlot('DESC')
}
