import { highlight, message } from '@kdt310722/logger'
import { formatUsdCurrency } from '@kdt310722/utils/number'
import { fetchWhirlpool, getWhirlpoolDecoder } from '@orca-so/whirlpools-client'
import { sqrtPriceToPrice } from '@orca-so/whirlpools-core'
import { USDC_DECIMALS, WSOL_DECIMALS, WSOL_USDC_PAIR_ADDRESS } from '../constants'
import { datasource } from './datasource'
import { createChildLogger } from './logger'
import { rpcClient } from './rpc-client'
import { rpcServer } from './rpc-server'

let price: number | undefined

function getPrice(sqrtPrice: bigint) {
    return sqrtPriceToPrice(sqrtPrice, WSOL_DECIMALS, USDC_DECIMALS)
}

const fetchSolPrice = async () => fetchWhirlpool(rpcClient, WSOL_USDC_PAIR_ADDRESS, { commitment: 'confirmed' }).then((account) => {
    return getPrice(account.data.sqrtPrice)
})

rpcServer.ws.addEvent('solPrice')

export async function initSolPrice() {
    return price ??= await fetchSolPrice()
}

const logger = createChildLogger('common:sol-price')
const decoder = getWhirlpoolDecoder()

async function subscribe() {
    const subscription = datasource.createAccountSubscription(WSOL_USDC_PAIR_ADDRESS)

    subscription.onData(({ data }) => {
        const newPrice = price = getPrice(decoder.decode(data).sqrtPrice)

        rpcServer.ws.emit('solPrice', newPrice)
        logger.debug(message(() => `Current SOL price: ${highlight(formatUsdCurrency(newPrice))}`))
    })

    await subscription.subscribe()
}

export async function initializeSolPrice() {
    const stop = logger.createLoading().start('Initializing sol price module...')

    await subscribe().then(() => initSolPrice()).then((price) => {
        stop(`Current SOL price: ${highlight(formatUsdCurrency(price))}`)
    })
}
