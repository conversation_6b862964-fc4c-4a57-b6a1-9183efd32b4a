import { isAddress } from '@solana/kit'
import { RpcServer } from '../modules/rpc-server/server'
import { config } from '../config'
import { registerRoutes } from '../modules/rpc-server/utils/routes'
import { routesPath } from '../utils/path'
import { toBondingCurveAccount } from '../utils/formatters/to-bonding-curve-account'
import { createChildLogger } from './logger'
import { slotIndexer } from './slot-indexer'
import { pumpfunIndexer } from './pumpfun-indexer'
import { gapFill } from './gap'
import { datasource } from './datasource'

export const rpcServerLogger = createChildLogger('common:rpc-server')
export const rpcServer = new RpcServer(config.rpcServer.host, config.rpcServer.port, config.rpcServer)

rpcServer.ws.addEvent('latency')
rpcServer.ws.addEvent('slot')
rpcServer.ws.addEvent('globalAccount')

rpcServer.ws.addEvent('token')
rpcServer.ws.addEvent('gapToken')

rpcServer.ws.addEvent('trade')
rpcServer.ws.addEvent('gapTrade')

rpcServer.ws.addEvent((name) => name.startsWith('token:') && isAddress(name.slice(6)))
rpcServer.ws.addEvent((name) => name.startsWith('bondingCurve:') && isAddress(name.slice(13)))
rpcServer.ws.addEvent((name) => name.startsWith('trade:') && isAddress(name.slice(6)))
rpcServer.ws.addEvent((name) => name.startsWith('user_trade:') && isAddress(name.slice(11)))
rpcServer.ws.addEvent((name) => name.startsWith('gapTrade:') && isAddress(name.slice(9)))

datasource.on('latency', (latency) => {
    rpcServer.ws.emit('latency', Number(latency / 1_000_000n))
})

slotIndexer.on('slot', ({ slot }) => {
    rpcServer.ws.emit('slot', slot)
})

pumpfunIndexer.on('globalAccount', (data) => rpcServer.ws.emit('globalAccount', data))
pumpfunIndexer.on('token', (token) => rpcServer.ws.emit('token', token))
pumpfunIndexer.on('complete', (token) => rpcServer.ws.emit(`token:${token}`, { isCompleted: true }))

pumpfunIndexer.on('trade', (trade) => {
    rpcServer.ws.emit('trade', trade)
    rpcServer.ws.emit(`trade:${trade.mint}`, trade)
    rpcServer.ws.emit(`user_trade:${trade.user}`, trade)
    rpcServer.ws.emit(`bondingCurve:${trade.mint}`, toBondingCurveAccount(trade))
})

gapFill.on('tokens', (tokens) => {
    for (const token of tokens) {
        rpcServer.ws.emit('gapToken', token)
    }
})

gapFill.on('trades', (trades) => {
    for (const trade of trades) {
        rpcServer.ws.emit('gapTrade', trade)
        rpcServer.ws.emit(`gapTrade:${trade.mint}`, trade)
    }
})

export async function initializeRpcServer() {
    const stop = rpcServerLogger.createLoading().start('Initializing RPC server...')

    await registerRoutes(routesPath(), rpcServer).then(() => {
        stop('RPC server is initialized!')
    })
}

export async function startRpcServer() {
    await rpcServer.start()
}
