import { config } from '../config'
import { GeyserDatasource } from '../modules/datasource/datasources/geyser/geyser'
import type { Datasource } from '../modules/datasource/datasource'
import { RpcDatasource } from '../modules/datasource/datasources/rpc/rpc'

export function createDatasource(): Datasource {
    if (config.datasource.type === 'geyser') {
        return new GeyserDatasource(config.datasource.geyser)
    }

    if (config.datasource.type === 'rpc') {
        return new RpcDatasource(config.datasource.rpc.url, {
            reconnect: config.datasource.rpc.reconnect,
            connectTimeout: config.datasource.rpc.timeout.connect,
            disconnectTimeout: config.datasource.rpc.timeout.disconnect,
            requestTimeout: config.datasource.rpc.timeout.request,
        })
    }

    throw new Error(`Unknown datasource: ${JSON.stringify(config.datasource)}`)
}

export const datasource = createDatasource()

export async function initializeDatasource() {
    await datasource.initialize()
}
