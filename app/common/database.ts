import { DataSource } from 'typeorm'
import type { Nullable } from '@kdt310722/utils/common'
import { DatabaseLogger } from '../utils/database/logger'
import { NamingStrategy } from '../utils/database/naming-strategy'
import { appPath } from '../utils/path'
import { config } from '../config'
import { dropCandlestickViews } from '../entities/candlestick'
import { createChildLogger } from './logger'

export const databaseLogger = createChildLogger('database')

export const database = new DataSource({
    ...config.database,
    type: 'postgres',
    database: config.database.name,
    entities: [appPath('entities', '*.ts')],
    migrations: [appPath('migrations', '*.ts')],
    migrationsRun: config.database.runMigrations,
    logger: new DatabaseLogger(databaseLogger, config.database.logging),
    namingStrategy: new NamingStrategy(),
    dropSchema: false,
})

export function setMaxQueryExecutionTime(time: Nullable<number>) {
    return database.setOptions({ maxQueryExecutionTime: time ?? undefined })
}

export async function initializeDatabase() {
    const complete = databaseLogger.createLoading().start('Initializing database...')

    if (config.database.dropSchema) {
        database.setOptions({ migrationsRun: false })

        await database.initialize()
        await dropCandlestickViews()
        await database.dropDatabase()
        await database.destroy()

        database.setOptions({ migrationsRun: true })
    }

    return Promise.resolve(setMaxQueryExecutionTime(null)).then(() => database.initialize()).then(() => setMaxQueryExecutionTime(config.database.maxQueryExecutionTime)).then(() => {
        complete('Database is initialized!')
    })
}
