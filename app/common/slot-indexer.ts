import { LogLevel, highlight, message } from '@kdt310722/logger'
import { format } from '@kdt310722/utils/number'
import { SlotIndexer } from '../modules/indexer/slot'
import { datasource } from './datasource'
import { createChildLogger } from './logger'
import { getSlots, rpcClient } from './rpc-client'

const logger = createChildLogger('indexers:slot')
const slotLogger = createChildLogger('indexers:slot:slot')
const indexer = new SlotIndexer(datasource)

indexer.on('slot', (slot) => {
    slotLogger.debug(message(() => `Current slot: ${highlight(slot.slot)}`))
})

const beforeGapHandlers = new Set<(start: number, end: number) => void>()
const gapHandlers = new Set<(start: number, end: number, slots: number[]) => void>()
const noMissingBlocksHandlers = new Set<(start: number, end: number) => void>()

export function onGap(handler: (start: number, end: number, slots: number[]) => void) {
    gapHandlers.add(handler)
}

export function onBeforeGap(handler: (start: number, end: number) => void) {
    beforeGapHandlers.add(handler)
}

export function onNoMissingBlocks(handler: (start: number, end: number) => void) {
    noMissingBlocksHandlers.add(handler)
}

export async function waitForRpcClientUpToDate(slot: number, logLevel: LogLevel = LogLevel.DEBUG, commitment: 'confirmed' | 'finalized' = 'finalized') {
    logger.log(logLevel, message(() => `Waiting for RPC client to catch up to slot ${highlight(slot)}...`))

    const catchup = async (): Promise<bigint> => {
        const latestSlot = await rpcClient.getSlot({ commitment }).send()

        if (latestSlot >= slot) {
            return latestSlot
        }

        return catchup()
    }

    await catchup().then((slot) => logger.log(logLevel, message(() => `Latest RPC client slot: ${highlight(slot)}`))).catch((error) => {
        logger.exit(1, 'fatal', 'Failed to wait for RPC client to catch up', error)
    })
}

export async function handleGap(start: number, end: number, logLevel: LogLevel = LogLevel.DEBUG) {
    for (const handler of beforeGapHandlers) {
        handler(start, end)
    }

    const slots = await waitForRpcClientUpToDate(end + 1, logLevel).then(() => logger.log(logLevel, message(() => `Finding missing blocks from slot ${highlight(start)} to ${highlight(end)}...`))).then(async () => getSlots(start, end))

    if (slots.length === 0) {
        logger.log(logLevel, message(() => `No missing blocks found for gap: ${highlight(start)} - ${highlight(end)}`))

        for (const handler of noMissingBlocksHandlers) {
            handler(start, end)
        }
    } else {
        logger.warn(`Gap found: ${highlight(slots[0])} - ${highlight(slots.at(-1))} (${highlight(format(slots.length))} slots)`)

        for (const handler of gapHandlers) {
            handler(start, end, slots)
        }
    }
}

indexer.on('gap', (start, end) => {
    handleGap(start, end).catch((error) => logger.exit(1, 'fatal', `Failed to handle gap: ${start} - ${end}`, error))
})

indexer.on('waiterRemoved', (slot) => {
    logger.debug(message(() => `Removed waiter for slot: ${highlight(slot)}`))
})

indexer.on('unhealthy', (reason) => {
    logger.error(`Slot indexer is unhealthy: ${highlight(reason)}`)
})

export const slotIndexer = indexer

export async function initializeSlotIndexer() {
    const stop = logger.createLoading().start('Listening for slot updates...')

    await indexer.start().then(() => {
        stop('Slot indexer started!')
    })
}
