import { isArray } from '@kdt310722/utils/array'
import { isString } from '@kdt310722/utils/string'
import { z } from 'zod'
import { address } from '../utils/schemas/address'
import { handler as getMultipleBondingCurves } from './get-multiple-bonding-curves'

const objSchema = z.object({
    mint: address,
})

export const schema = z.union([objSchema, address, z.tuple([address])]).transform((value) => {
    return isArray(value) ? { mint: value[0] } : (isString(value) ? { mint: value } : value)
})

export const handler = async ({ mint }: z.infer<typeof schema>) => {
    return getMultipleBondingCurves({ mints: [mint] }).then((r) => r[0] ?? null)
}
