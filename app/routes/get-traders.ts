import type { z } from 'zod'
import { tradeRepository } from '../common/entities'
import { schema as mintsSchema } from './get-multiple-bonding-curves'

export const schema = mintsSchema

export const handler = async ({ mints }: z.infer<typeof schema>) => {
    const query = tradeRepository.createQueryBuilder('trade')

    query.select('trade.mint', 'mint')
    query.addSelect('trade.user', 'user')
    query.where('trade.mint IN (:...mints)', { mints })
    query.groupBy('trade.mint')
    query.addGroupBy('trade.user')

    const data = await query.getRawMany()
    const result: Record<string, string[]> = {}

    for (const mint of mints) {
        result[mint] = []
    }

    for (const { mint, user } of data) {
        result[mint].push(user)
    }

    return result
}
