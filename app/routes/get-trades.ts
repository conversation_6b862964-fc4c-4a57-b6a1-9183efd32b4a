import { z } from 'zod'
import { type FindManyOptions, type FindOptionsWhere, In } from 'typeorm'
import { notNullish } from '@kdt310722/utils/common'
import { BetweenRange } from '../utils/database/operators/between-range'
import { BetweenTimestamp } from '../utils/database/operators/between-timestamp'
import { tradeRepository } from '../common/entities'
import type { Trade } from '../entities/trade'
import { getSortKey } from '../utils/keys'
import { MAX_TRANSACTION_INDEX } from '../constants'
import { databaseFilter } from './rules/database-filter'
import { addresses } from './rules/addresses'
import { signatures } from './rules/signatures'
import { bigint } from './rules/bigint'

const baseTradeFilter = databaseFilter.omit({ startSlot: true, endSlot: true }).extend({
    mints: addresses.nullish(),
    count: z.boolean().nullish().transform((v) => v ?? true),
    users: addresses.nullish(),
    signatures: signatures.nullish(),
})

export const tradesFilterWithSlot = databaseFilter.pick({ startSlot: true, endSlot: true }).extend({
    startTransactionIndex: z.number().int().min(0).nullish(),
    endTransactionIndex: z.number().int().min(0).nullish(),
    startEventIndex: z.number().int().min(0).nullish(),
    endEventIndex: z.number().int().min(0).nullish(),
})

export const tradesFilterWithSortKey = z.object({
    startSortKey: bigint.nullish(),
    endSortKey: bigint.nullish(),
})

export const tradesFilter = baseTradeFilter.merge(tradesFilterWithSlot).merge(tradesFilterWithSortKey)

export type GetSortKeyFilterParams = z.infer<typeof tradesFilterWithSlot> & z.infer<typeof tradesFilterWithSortKey>

export function getSortKeyFilter({ startSlot, endSlot, startTransactionIndex, endTransactionIndex, startEventIndex, endEventIndex, startSortKey, endSortKey }: GetSortKeyFilterParams) {
    const result: { startSortKey: bigint | undefined, endSortKey: bigint | undefined } = { startSortKey: undefined, endSortKey: undefined }

    if (notNullish(startSortKey)) {
        result.startSortKey = startSortKey
    } else if (notNullish(startSlot)) {
        result.startSortKey = getSortKey(startSlot, startTransactionIndex ?? 0, startEventIndex ?? 0)
    }

    if (notNullish(endSortKey)) {
        result.endSortKey = endSortKey
    } else if (notNullish(endSlot)) {
        result.endSortKey = getSortKey(endSlot, endTransactionIndex ?? MAX_TRANSACTION_INDEX, endEventIndex ?? 0)
    }

    return result
}

export const schema = tradesFilter

export const handler = async ({ mints, count, users, signatures, startTime, endTime, sortDirection, limit, offset, ...filter }: z.infer<typeof schema>) => {
    const { startSortKey, endSortKey } = getSortKeyFilter(filter)

    const whereMints = notNullish(mints) ? { mint: In(mints) } : {}
    const whereUsers = notNullish(users) ? { user: In(users) } : {}
    const whereSignatures = notNullish(signatures) ? { signature: In(signatures) } : {}
    const where: FindOptionsWhere<Trade> = { ...whereUsers, ...whereSignatures, ...whereMints, sortKey: BetweenRange(startSortKey, endSortKey), timestamp: BetweenTimestamp(startTime, endTime) }
    const queryParams: FindManyOptions<Trade> = { where, order: { sortKey: sortDirection }, take: limit, skip: offset }

    if (count) {
        return tradeRepository.findAndCount(queryParams).then(([trades, total]) => ({ total, trades }))
    }

    return tradeRepository.find(queryParams)
}
