import { z } from 'zod'
import { type FindManyOptions, type FindOptionsWhere, In } from 'typeorm'
import { notNullish } from '@kdt310722/utils/common'
import { optionalObject } from '../utils/schemas/optional-object'
import type { Token } from '../entities/token'
import { BetweenRange } from '../utils/database/operators/between-range'
import { BetweenTimestamp } from '../utils/database/operators/between-timestamp'
import { tokenRepository } from '../common/entities'
import { databaseFilter } from './rules/database-filter'
import { addresses } from './rules/addresses'

export const schema = optionalObject(databaseFilter.extend({
    count: z.boolean().nullish().transform((v) => v ?? true),
    mints: addresses.nullish(),
    creators: addresses.nullish(),
}))

export const handler = async ({ count, mints, creators, startSlot, endSlot, startTime, endTime, sortDirection, limit, offset }: z.infer<typeof schema>) => {
    const whereMints = notNullish(mints) ? { mint: In(mints) } : {}
    const whereCreators = notNullish(creators) ? { createdBy: In(creators) } : {}
    const where: FindOptionsWhere<Token> = { ...whereMints, ...whereCreators, createdAtSlot: BetweenRange(startSlot, endSlot), createdAt: BetweenTimestamp(startTime, endTime) }
    const queryParams: FindManyOptions<Token> = { where, order: { createdAtSlot: sortDirection }, take: limit, skip: offset }

    if (count) {
        return tokenRepository.findAndCount(queryParams).then(([tokens, total]) => ({ total, tokens }))
    }

    return tokenRepository.find(queryParams)
}
