import { z } from 'zod'
import { notNullish } from '@kdt310722/utils/common'
import type { SelectQueryBuilder } from 'typeorm'
import { tradeRepository } from '../common/entities'
import { database } from '../common/database'
import type { Trade } from '../entities/trade'
import { addresses } from './rules/addresses'
import { getSortKeyFilter, tradesFilterWithSlot, tradesFilterWithSortKey } from './get-trades'

export const options = tradesFilterWithSlot.merge(tradesFilterWithSortKey).default({})

export const schema = z.union([z.tuple([addresses]), z.tuple([addresses, options])]).transform((value) => {
    return value.length === 1 ? <const>[value[0], options.parse({})] : value
})

export const handler = async ([mints, filter]: z.infer<typeof schema>) => {
    const filteredTrades = tradeRepository.createQueryBuilder('trade')
    const aggregatedTrades = tradeRepository.createQueryBuilder('trade')
    const { startSortKey, endSortKey } = getSortKeyFilter(filter)

    const where = (query: SelectQueryBuilder<Trade>) => {
        query.where('trade.mint IN (:...mints)', { mints })

        if (notNullish(startSortKey)) {
            query.andWhere('trade.sortKey >= :startSortKey', { startSortKey })
        }

        if (notNullish(endSortKey)) {
            query.andWhere('trade.sortKey <= :endSortKey', { endSortKey })
        }
    }

    filteredTrades.select('trade.mint', 'mint')
    filteredTrades.addSelect('trade.sortKey', 'sortKey')
    filteredTrades.addSelect('trade.signature', 'signature')
    filteredTrades.addSelect('trade.slot', 'slot')
    filteredTrades.addSelect('trade.transactionIndex', 'transactionIndex')
    filteredTrades.addSelect('trade.eventIndex', 'eventIndex')
    filteredTrades.addSelect('trade.timestamp', 'timestamp')
    filteredTrades.addSelect('ROW_NUMBER() OVER (PARTITION BY trade.mint ORDER BY trade.sortKey DESC)', 'rnk')

    aggregatedTrades.select('trade.mint', 'mint')
    aggregatedTrades.addSelect('MIN(trade.tokenPrice)', 'atl')
    aggregatedTrades.addSelect('MAX(trade.tokenPrice)', 'ath')
    aggregatedTrades.addSelect('SUM(trade.solAmount) FILTER (WHERE trade.isBuy = true)', 'buyVolume')
    aggregatedTrades.addSelect('SUM(trade.solAmount) FILTER (WHERE trade.isBuy = false)', 'sellVolume')
    aggregatedTrades.addSelect('COUNT(*) FILTER (WHERE trade.isBuy = true)', 'buysCount')
    aggregatedTrades.addSelect('COUNT(*) FILTER (WHERE trade.isBuy = false)', 'sellsCount')
    aggregatedTrades.addSelect('COUNT(DISTINCT trade.user)', 'tradersCount')
    aggregatedTrades.addSelect('COUNT(DISTINCT CASE WHEN trade.isBuy THEN trade.user END)', 'buyersCount')
    aggregatedTrades.addSelect('COUNT(DISTINCT CASE WHEN NOT trade.isBuy THEN trade.user END)', 'sellersCount')
    aggregatedTrades.groupBy('trade.mint')

    where(filteredTrades)
    where(aggregatedTrades)

    const statsQuery = database.manager.createQueryBuilder()

    statsQuery.select('"ft"."mint"', 'mint')
    statsQuery.addSelect('"ft"."sortKey"', 'sortKey')
    statsQuery.addSelect('"ft"."signature"', 'signature')
    statsQuery.addSelect('"ft"."slot"', 'slot')
    statsQuery.addSelect('"ft"."transactionIndex"', 'transactionIndex')
    statsQuery.addSelect('"ft"."eventIndex"', 'eventIndex')
    statsQuery.addSelect('"at"."atl"', 'atl')
    statsQuery.addSelect('"at"."ath"', 'ath')
    statsQuery.addSelect('"at"."buyVolume"', 'buyVolume')
    statsQuery.addSelect('"at"."sellVolume"', 'sellVolume')
    statsQuery.addSelect('CAST("at"."buysCount" AS INTEGER)', 'buysCount')
    statsQuery.addSelect('CAST("at"."sellsCount" AS INTEGER)', 'sellsCount')
    statsQuery.addSelect('CAST("at"."tradersCount" AS INTEGER)', 'tradersCount')
    statsQuery.addSelect('CAST("at"."buyersCount" AS INTEGER)', 'buyersCount')
    statsQuery.addSelect('CAST("at"."sellersCount" AS INTEGER)', 'sellersCount')
    statsQuery.addSelect('"ft"."timestamp"', 'lastTrade')
    statsQuery.from(`(${filteredTrades.getQuery()})`, 'ft')
    statsQuery.innerJoin(`(${aggregatedTrades.getQuery()})`, 'at', '"ft"."mint" = "at"."mint"')
    statsQuery.where('"ft"."rnk" = 1')
    statsQuery.setParameters(filteredTrades.getParameters())
    statsQuery.setParameters(aggregatedTrades.getParameters())

    const result = await statsQuery.getRawMany().then((r) => Object.fromEntries(
        r.map((i) => [i.mint, { ...i, sortKey: BigInt(i.sortKey), atl: BigInt(i.atl), ath: BigInt(i.ath), buyVolume: notNullish(i.buyVolume) ? BigInt(i.buyVolume) : 0n, sellVolume: notNullish(i.sellVolume) ? BigInt(i.sellVolume) : 0n }]),
    ))

    return mints.map((mint) => result[mint] ?? null)
}
