import { z } from 'zod'
import { isString } from '@kdt310722/utils/string'
import { address } from '../utils/schemas/address'
import { handler as getMultipleTokenStats, options } from './get-multiple-token-stats'

export const schema = z.union([address, z.tuple([address]), z.tuple([address, options])]).transform((value) => {
    return isString(value) ? <const>[value, options.parse({})] : (value.length === 1 ? <const>[value[0], options.parse({})] : value)
})

export const handler = async ([mint, filter]: z.infer<typeof schema>) => {
    return getMultipleTokenStats([[mint], filter]).then((r) => r[0])
}
