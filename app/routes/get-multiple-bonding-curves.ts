import { z } from 'zod'
import { isArray } from '@kdt310722/utils/array'
import { isString } from '@kdt310722/utils/string'
import { parseJson } from '@kdt310722/utils/json'
import { tradeRepository } from '../common/entities'
import { toBondingCurveAccount } from '../utils/formatters/to-bonding-curve-account'
import { addresses } from './rules/addresses'

const objSchema = z.object({
    mints: addresses,
})

export const schema = z.union([objSchema, addresses]).transform((value) => {
    return isArray(value) ? { mints: value } : value
})

export const handler = async ({ mints }: z.infer<typeof schema>) => {
    const query = tradeRepository.createQueryBuilder('trade')

    query.distinctOn(['trade.mint'])
    query.select('trade.mint', 'mint')
    query.addSelect('trade.slot', 'slot')
    query.addSelect('trade.sortKey', 'sortKey')
    query.addSelect('trade.transactionIndex', 'transactionIndex')
    query.addSelect('trade.signature', 'signature')
    query.addSelect('trade.eventIndex', 'eventIndex')
    query.addSelect('trade.virtualSolReserves', 'virtualSolReserves')
    query.addSelect('trade.virtualTokenReserves', 'virtualTokenReserves')
    query.addSelect('trade.realSolReserves', 'realSolReserves')
    query.addSelect('trade.realTokenReserves', 'realTokenReserves')
    query.addSelect('trade.metadata', 'metadata')

    query.where('trade.mint IN (:...mints)', { mints })
    query.orderBy('trade.mint, trade.sortKey', 'DESC')

    const result = await query.getRawMany().then((trades) => Object.fromEntries(trades.map((trade) => {
        if (isString(trade.metadata)) {
            trade.metadata = parseJson(trade.metadata)
        }

        return [trade.mint, toBondingCurveAccount(trade)]
    })))

    return mints.map((mint) => result[mint] ?? null)
}
