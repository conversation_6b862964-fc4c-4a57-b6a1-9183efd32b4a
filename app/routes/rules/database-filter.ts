import { z } from 'zod'
import { config } from '../../config'

export const databaseFilter = z.object({
    limit: z.number().int().min(1).max(config.rpcServer.itemsLimit).nullish().transform((v) => v ?? config.rpcServer.itemsLimit),
    offset: z.number().int().min(0).nullish().transform((v) => v ?? 0),
    startTime: z.number().int().min(0).nullish(),
    endTime: z.number().int().min(0).nullish(),
    startSlot: z.number().int().min(0).nullish(),
    endSlot: z.number().int().min(0).nullish(),
    sortDirection: z.union([z.literal('ASC'), z.literal('DESC')]).nullish().transform((v) => v ?? 'DESC'),
})
