import type { z } from 'zod'
import { In } from 'typeorm'
import { tokenRepository } from '../common/entities'
import type { Token } from '../entities/token'
import { schema as mintsSchema } from './get-multiple-bonding-curves'

export const schema = mintsSchema

export const handler = async ({ mints }: z.infer<typeof schema>) => {
    const entities = await tokenRepository.find({ where: { mint: In(mints) } }).then((entities) => {
        return Object.fromEntries(entities.map((entity) => <const>[entity.mint, entity]))
    })

    return mints.map((mint): Token | null => entities[mint] ?? null)
}
