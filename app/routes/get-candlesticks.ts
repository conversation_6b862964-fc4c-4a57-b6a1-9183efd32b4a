import { z } from 'zod'
import type { ObjectLiteral, SelectQueryBuilder } from 'typeorm'
import { notNullish } from '@kdt310722/utils/common'
import { omit } from '@kdt310722/utils/object'
import { address } from '../utils/schemas/address'
import { CANDLESTICK_INTERVALS, type Interval } from '../constants'
import { database } from '../common/database'
import { getCandleSticksQuery, getCandlestickEntity } from '../entities/candlestick'
import { toCandlestickEntity } from '../utils/formatters/to-candlestick-entity'
import { config } from '../config'
import { databaseFilter } from './rules/database-filter'

const filter = databaseFilter.pick({
    limit: true,
    offset: true,
    sortDirection: true,
})

export const interval = z.enum(Object.keys(CANDLESTICK_INTERVALS) as [Interval, ...Interval[]])

export const schema = z.union([z.tuple([address, interval]), z.tuple([address, interval, filter])]).transform((val) => {
    return val.length === 2 ? <const>[val[0], val[1], filter.parse({})] : val
})

export const handler = async ([mint, interval, { limit, offset, sortDirection }]: z.infer<typeof schema>) => {
    const addSelect = (alias: string, query: SelectQueryBuilder<any>) => {
        const columns = ['timestamp', 'open', 'high', 'low', 'close', 'volumeSol', 'volumeToken', 'tradesCount']

        query.select(`"${alias}"."mint"`, 'mint')

        for (const column of columns) {
            query.addSelect(`"${alias}"."${column}"`, column)
        }

        return query
    }

    const addWhere = (alias: string, query: SelectQueryBuilder<any>) => {
        return query.where(`"${alias}"."mint" = :mint`, { mint })
    }

    const addPagination = (query: SelectQueryBuilder<any>) => {
        if (notNullish(limit)) {
            query.limit(limit)
        }

        if (notNullish(offset)) {
            query.offset(offset)
        }

        return query
    }

    const formatResult = (items: ObjectLiteral[]) => items.map((item) => omit(toCandlestickEntity(item), 'mint'))
    const latestQuery = addWhere('trade', getCandleSticksQuery(interval))

    if (!config.database.cacheCandlestick) {
        return addPagination(latestQuery).orderBy('"timestamp"', sortDirection).getRawMany().then(formatResult)
    }

    const entity = database.getRepository(getCandlestickEntity(interval))
    const cacheQuery = addSelect('candle', addWhere('candle', entity.createQueryBuilder('candle')))
    const wrappedLatestQuery = addSelect('l', database.manager.createQueryBuilder())
    const combinedQuery = addPagination(database.manager.createQueryBuilder())

    wrappedLatestQuery.from(`(${latestQuery.getQuery()})`, 'l')
    wrappedLatestQuery.leftJoin(entity.metadata.tableName, 'c', '"c"."mint" = "l"."mint" AND "c"."timestamp" = "l"."timestamp"')
    wrappedLatestQuery.where('"c"."timestamp" IS NULL')
    wrappedLatestQuery.orderBy('"timestamp"', sortDirection)

    combinedQuery.select('*')
    combinedQuery.from(`(${cacheQuery.getQuery()} UNION ALL ${wrappedLatestQuery.getQuery()})`, 'combined')

    combinedQuery.setParameters(cacheQuery.getParameters())
    combinedQuery.setParameters(latestQuery.getParameters())
    combinedQuery.setParameters(wrappedLatestQuery.getParameters())

    return combinedQuery.getRawMany().then(formatResult)
}
