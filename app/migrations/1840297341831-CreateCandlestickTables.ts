import type { MigrationInterface, QueryRunner } from 'typeorm'
import { CANDLESTICK_INTERVALS } from '../constants'
import { getCandleSticksQuery } from '../entities/candlestick'
import { config } from '../config'

export class CreateCandlestickTables1840297341831 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        if (!config.database.cacheCandlestick) {
            return
        }

        for (const [timeframe, { name, scheduleInterval }] of Object.entries(CANDLESTICK_INTERVALS)) {
            await queryRunner.query(`CREATE MATERIALIZED VIEW candlesticks_${timeframe} WITH (timescaledb.continuous) AS ${getCandleSticksQuery(timeframe).getQuery()} WITH NO DATA`)
            await queryRunner.query(`SELECT add_continuous_aggregate_policy('candlesticks_${timeframe}', start_offset => NULL, end_offset => INTERVAL '${name}', schedule_interval => INTERVAL '${scheduleInterval}')`)
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        for (const [timeframe] of Object.entries(CANDLESTICK_INTERVALS)) {
            await queryRunner.query(`DROP MATERIALIZED VIEW IF EXISTS candlesticks_${timeframe}`)
        }
    }
}
