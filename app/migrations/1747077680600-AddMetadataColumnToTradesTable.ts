import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddMetadataColumnToTradesTable1747077680600 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "trades" ADD "metadata" json NOT NULL`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "trades" DROP COLUMN "metadata"`)
    }
}
