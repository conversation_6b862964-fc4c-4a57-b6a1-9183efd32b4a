import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateTokensTable1739813156749 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "tokens" ("mint" character varying NOT NULL, "name" character varying NOT NULL, "symbol" character varying NOT NULL, "decimals" integer NOT NULL, "metadata_uri" character varying NOT NULL, "is_completed" boolean NOT NULL, "created_by" character varying NOT NULL, "created_at_slot" integer NOT NULL, "created_at_transaction" character varying NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT "PK_685c2d9752ce327e8280c556d4d" PRIMARY KEY ("mint"))`)
        await queryRunner.query(`CREATE INDEX "IDX_1c82aed805dd3e769e54356ff5" ON "tokens" ("is_completed") `)
        await queryRunner.query(`CREATE INDEX "IDX_b0676189524a0712ae09aca7c2" ON "tokens" ("created_by") `)
        await queryRunner.query(`CREATE INDEX "IDX_c0cc0fd61eb141023abd225d62" ON "tokens" ("created_at_slot") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_c0cc0fd61eb141023abd225d62"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_b0676189524a0712ae09aca7c2"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_1c82aed805dd3e769e54356ff5"`)
        await queryRunner.query(`DROP TABLE "tokens"`)
    }
}
