import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddIndexToTransactionIndexColumn1740428659330 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE INDEX "IDX_b8974346b489ca92f262b885f4" ON "trades" ("transaction_index") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_b8974346b489ca92f262b885f4"`)
    }
}
