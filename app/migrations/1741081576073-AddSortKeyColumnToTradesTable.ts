import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddSortKeyColumnToTradesTable1741081576073 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "trades" ADD "sort_key" bigint NOT NULL`)
        await queryRunner.query(`CREATE INDEX "IDX_35ad1fef76f83787b6e0f6a702" ON "trades" ("sort_key") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_35ad1fef76f83787b6e0f6a702"`)
        await queryRunner.query(`ALTER TABLE "trades" DROP COLUMN "sort_key"`)
    }
}
