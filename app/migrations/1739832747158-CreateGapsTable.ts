import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateGapsTable1739832747158 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "gaps" ("id" SERIAL NOT NULL, "start_slot" integer NOT NULL, "end_slot" integer NOT NULL, "slots" text, "filled_slot" integer, "is_resolved" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_ef311f9593d985bb2d81ebb8000" PRIMARY KEY ("id"))`)
        await queryRunner.query(`CREATE INDEX "IDX_0221f980b0aa10857b29eb8aa3" ON "gaps" ("start_slot") `)
        await queryRunner.query(`CREATE INDEX "IDX_4fd8a0ab52e6a0e22afef0a228" ON "gaps" ("end_slot") `)
        await queryRunner.query(`CREATE INDEX "IDX_ae3a214325ed43aa0b4c1b54a2" ON "gaps" ("is_resolved") `)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_ae3a214325ed43aa0b4c1b54a2"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_4fd8a0ab52e6a0e22afef0a228"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_0221f980b0aa10857b29eb8aa3"`)
        await queryRunner.query(`DROP TABLE "gaps"`)
    }
}
