import type { MigrationInterface, QueryRunner } from 'typeorm'

export class AddTokenPriceBeforeColumnToTradesTable1740290882342 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "trades" ADD "token_price_before" bigint NOT NULL`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "trades" DROP COLUMN "token_price_before"`)
    }
}
