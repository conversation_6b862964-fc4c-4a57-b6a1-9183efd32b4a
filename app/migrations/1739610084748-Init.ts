import type { MigrationInterface, QueryRunner } from 'typeorm'

export class Init1739610084748 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE')
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('DROP EXTENSION IF EXISTS timescaledb')
    }
}
