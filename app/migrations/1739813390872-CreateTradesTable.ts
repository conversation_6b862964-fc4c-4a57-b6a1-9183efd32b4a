import type { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateTradesTable1739813390872 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "trades" ("slot" integer NOT NULL, "transaction_index" integer NOT NULL, "signature" character varying NOT NULL, "event_index" integer NOT NULL, "user" character varying NOT NULL, "mint" character varying NOT NULL, "is_buy" boolean NOT NULL, "sol_amount" bigint NOT NULL, "token_amount" bigint NOT NULL, "virtual_sol_reserves" bigint NOT NULL, "virtual_token_reserves" bigint NOT NULL, "real_sol_reserves" bigint NOT NULL, "real_token_reserves" bigint NOT NULL, "token_price" bigint NOT NULL, "timestamp" TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT "PK_ae90f10d8b1a8a4faaf9ebb125e" PRIMARY KEY ("signature", "event_index", "timestamp"))`)
        await queryRunner.query(`CREATE INDEX "IDX_0154d150458d0e0037d93f8c6a" ON "trades" ("slot") `)
        await queryRunner.query(`CREATE INDEX "IDX_809c1f02f3df6af530ae12bf01" ON "trades" ("user") `)
        await queryRunner.query(`CREATE INDEX "IDX_6fd1eaa0588ce88667c034b096" ON "trades" ("mint") `)
        await queryRunner.query(`SELECT create_hypertable('trades', by_range('timestamp', INTERVAL '2 hour'), if_not_exists => TRUE, migrate_data => TRUE)`)
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_6fd1eaa0588ce88667c034b096"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_809c1f02f3df6af530ae12bf01"`)
        await queryRunner.query(`DROP INDEX "public"."IDX_0154d150458d0e0037d93f8c6a"`)
        await queryRunner.query(`DROP TABLE "trades"`)
    }
}
