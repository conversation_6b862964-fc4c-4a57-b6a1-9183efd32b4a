import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm'

@Entity()
export class Gap {
    @PrimaryGeneratedColumn()
    public declare id: number

    @Index()
    @Column()
    public declare startSlot: number

    @Index()
    @Column()
    public declare endSlot: number

    @Column({ type: 'simple-array', nullable: true })
    public declare slots?: number[]

    @Column({ nullable: true })
    public declare filledSlot?: number

    @Index()
    @Column({ default: false })
    public declare isResolved: boolean

    @CreateDateColumn()
    public declare createdAt: Date

    @UpdateDateColumn()
    public declare updatedAt: Date
}
