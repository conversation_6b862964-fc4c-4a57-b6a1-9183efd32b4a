export class BondingCurveAccount {
    public declare slot: number
    public declare sortKey: bigint
    public declare signature: string
    public declare transactionIndex: number
    public declare eventIndex: number
    public declare virtualTokenReserves: bigint
    public declare virtualSolReserves: bigint
    public declare realTokenReserves: bigint
    public declare realSolReserves: bigint
    public declare creator: string
    public declare feeBasisPoints?: bigint
    public declare creatorFeeBasisPoints?: bigint
}
