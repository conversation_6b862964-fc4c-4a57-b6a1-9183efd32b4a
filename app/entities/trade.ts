import { Column, Entity, Index, PrimaryColumn } from 'typeorm'
import { BigIntColumn } from '../utils/database/columns/bigint-column'
import { JsonColumn } from '../utils/database/columns/json-column'

export interface TradeMetadata {
    feeRecipient: string
    feeBasisPoints: bigint
    fee: bigint
    creator: string
    creatorFeeBasisPoints: bigint
    creatorFee: bigint
}

@Entity()
export class Trade {
    @Index()
    @Column()
    public declare slot: number

    @Index()
    @BigIntColumn()
    public declare sortKey: bigint

    @Index()
    @Column()
    public declare transactionIndex: number

    @PrimaryColumn()
    public declare signature: string

    @PrimaryColumn()
    public declare eventIndex: number

    @Index()
    @Column()
    public declare user: string

    @Index()
    @Column()
    public declare mint: string

    @Column()
    public declare isBuy: boolean

    @BigIntColumn()
    public declare solAmount: bigint

    @BigIntColumn()
    public declare tokenAmount: bigint

    @BigIntColumn()
    public declare virtualSolReserves: bigint

    @BigIntColumn()
    public declare virtualTokenReserves: bigint

    @BigIntColumn()
    public declare realSolReserves: bigint

    @BigIntColumn()
    public declare realTokenReserves: bigint

    @BigIntColumn()
    public declare tokenPriceBefore: bigint

    @BigIntColumn()
    public declare tokenPrice: bigint

    @JsonColumn()
    public declare metadata?: TradeMetadata

    @Index()
    @PrimaryColumn({ type: 'timestamptz' })
    public declare timestamp: Date
}
