import { Column, Entity, Index, PrimaryColumn } from 'typeorm'
import { SanitizedStringColumn } from '../utils/database/columns/sanitized-string'

@Entity()
export class Token {
    @PrimaryColumn()
    public declare mint: string

    @SanitizedStringColumn()
    public declare name: string

    @SanitizedStringColumn()
    public declare symbol: string

    @Column()
    public declare decimals: number

    @SanitizedStringColumn()
    public declare metadataUri: string

    @Index()
    @Column()
    public declare isCompleted: boolean

    @Index()
    @Column()
    public declare createdBy: string

    @Index()
    @Column()
    public declare createdAtSlot: number

    @Column()
    public declare createdAtTransaction: string

    @Column({ type: 'timestamptz' })
    public declare createdAt: Date
}
