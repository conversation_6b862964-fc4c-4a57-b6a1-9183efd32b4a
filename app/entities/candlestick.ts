import { EntitySchema } from 'typeorm'
import { CANDLESTICK_INTERVALS } from '../constants'
import { database } from '../common/database'
import { Trade } from './trade'

export interface Candlestick {
    timestamp: Date
    mint: string
    open: bigint
    high: bigint
    low: bigint
    close: bigint
    volumeSol: bigint
    volumeToken: bigint
    tradesCount: number
}

export const createCandlestickEntity = (timeframe: string) => new EntitySchema<Candlestick>({
    name: `Candlesticks${timeframe}`,
    tableName: `candlesticks_${timeframe}`,
    columns: {
        mint: { type: String, primary: true },
        timestamp: { type: 'timestamptz', primary: true },
        open: { type: 'bigint' },
        high: { type: 'bigint' },
        low: { type: 'bigint' },
        close: { type: 'bigint' },
        volumeSol: { type: 'bigint', name: 'volumeSol' },
        volumeToken: { type: 'bigint', name: 'volumeToken' },
        tradesCount: { type: 'int', name: 'tradesCount' },
    },
})

export type CandlestickEntity = ReturnType<typeof createCandlestickEntity>

export type Timeframe = keyof typeof CANDLESTICK_INTERVALS

export const candlestickEntities = Object.fromEntries(
    Object.keys(CANDLESTICK_INTERVALS).map((interval) => <const>[interval, createCandlestickEntity(interval)]),
)

export function getCandlestickEntity(timeframe: Timeframe) {
    return candlestickEntities[timeframe]
}

export async function dropCandlestickViews() {
    for (const [timeframe] of Object.entries(CANDLESTICK_INTERVALS)) {
        await database.query(`DROP MATERIALIZED VIEW IF EXISTS candlesticks_${timeframe} CASCADE`)
    }
}

export function getCandleSticksQuery(timeframe: string) {
    const query = database.getRepository(Trade).createQueryBuilder('trade')

    query.select('trade.mint', 'mint')
    query.addSelect(`time_bucket('${CANDLESTICK_INTERVALS[timeframe].name}', trade.timestamp)`, 'timestamp')
    query.addSelect('FIRST(trade.tokenPriceBefore, trade.sortKey)', 'open')
    query.addSelect('GREATEST(FIRST(trade.tokenPriceBefore, trade.sortKey), MAX(trade.tokenPrice))', 'high')
    query.addSelect('LEAST(FIRST(trade.tokenPriceBefore, trade.sortKey), MIN(trade.tokenPrice))', 'low')
    query.addSelect('LAST(trade.tokenPrice, trade.sortKey)', 'close')
    query.addSelect('SUM(trade.solAmount)', 'volumeSol')
    query.addSelect('SUM(trade.tokenAmount)', 'volumeToken')
    query.addSelect('COUNT(*)', 'tradesCount')
    query.groupBy('trade.mint')
    query.addGroupBy(`time_bucket('${CANDLESTICK_INTERVALS[timeframe].name}', trade.timestamp)`)

    return query
}
