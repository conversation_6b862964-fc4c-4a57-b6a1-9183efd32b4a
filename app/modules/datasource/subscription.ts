import type { DatasourceSubscription } from './types/subscription'

export abstract class Subscription<TData> implements DatasourceSubscription<TData> {
    protected readonly listeners: Array<(data: TData) => void> = []

    public onData(listener: (data: TData) => void) {
        this.listeners.push(listener)
    }

    public abstract subscribe(): Promise<void>

    public abstract unsubscribe(): Promise<void>

    protected emit(data: TData) {
        for (const listener of this.listeners) {
            listener(data)
        }
    }
}
