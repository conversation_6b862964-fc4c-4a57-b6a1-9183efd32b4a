import { RpcSubscription } from './rpc-subscription'

export class LatencySubscription extends RpcSubscription<bigint> {
    protected readonly subscribeMethod = 'slotsUpdatesSubscribe'
    protected readonly subscribeParams = undefined
    protected readonly unsubscribeMethod = 'slotsUpdatesUnsubscribe'

    protected handleData(data: any) {
        if (data.type === 'firstShredReceived') {
            this.emit(BigInt(Math.max((Date.now() - Number(data.timestamp)) * 1e6, 0)))
        }
    }
}
