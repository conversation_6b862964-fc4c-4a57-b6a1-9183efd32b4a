import { RpcWebSocketClient, type RpcWebsocketClientOptions } from '@kdt310722/rpc'
import { LogLevel, type Logger, highlight } from '@kdt310722/logger'
import { join } from '@kdt310722/utils/buffer'
import { tap, transform } from '@kdt310722/utils/function'
import { isEmpty } from '@kdt310722/utils/common'
import { Datasource } from '../../datasource'
import { createChildLogger } from '../../../../common/logger'
import { SlotSubscription } from './slot-subscription'
import { TransactionLogsSubscription } from './transaction-logs-subscription'
import { LatencySubscription } from './latency-subscription'
import { AccountSubscription } from './account-subscription'

export class RpcDatasource extends Datasource {
    protected readonly logger: Logger
    protected readonly client: RpcWebSocketClient

    protected isInitialized = false

    public constructor(public readonly url: string, options: RpcWebsocketClientOptions = {}) {
        super()

        this.logger = createChildLogger('datasources:rpc')
        this.client = this.createClient(url, options)
    }

    public get isConnected() {
        return this.client.socket.isConnected
    }

    public async disconnect(isExplicitly?: boolean) {
        return this.client.socket.disconnect(isExplicitly)
    }

    public async initialize() {
        const timer = tap(this.logger.createTimer(), () => this.logger.info('Initializing RPC datasource...'))
        const stop = this.logger.createLoading().start('Connecting to RPC websocket server...')

        await this.client.socket.connect().then(() => this.isInitialized = true).then(() => {
            stop('Connected to RPC websocket server!')
        })

        const latencyStop = this.logger.createLoading().start('Subscribing to latency updates...')
        const latency = this.createLatencySubscription()

        latency.onData((data) => this.addLatency(data))

        await latency.subscribe().then(() => latencyStop('Subscribed to latency updates!')).catch((error) => {
            latencyStop('Failed to subscribe to latency updates!', error)
        })

        this.logger.stopTimer(timer, 'info', 'RPC datasource initialized!')
    }

    public createLatencySubscription() {
        return new LatencySubscription(this.client, this.logger)
    }

    public createSlotSubscription() {
        return new SlotSubscription(this.client, this.logger)
    }

    public createTransactionLogsSubscription(account: string) {
        return new TransactionLogsSubscription(this.client, this.logger, account)
    }

    public createAccountSubscription(account: string) {
        return new AccountSubscription(this.client, this.logger, account)
    }

    protected handleConnected() {
        if (this.isInitialized) {
            this.logger.info('Connected to RPC websocket server!')
        }
    }

    protected handleDisconnected(code: number, reason: Buffer, isExplicitly: boolean) {
        if (!this.client.socket.isReconnecting) {
            this.logger.log(isExplicitly ? LogLevel.INFO : LogLevel.WARN, `Disconnected from RPC websocket server: ${highlight(`${code} - ${transform(join(reason), (r) => (isEmpty(r) ? 'EMPTY_REASON' : r))}`)}`)
        }

        if (this.client.socket.isReconnectAttemptReached) {
            this.logger.exit(1, 'fatal', 'Failed to reconnect to RPC websocket server')
        }
    }

    protected handleError(error: unknown) {
        Promise.resolve(this.logger.error('RPC websocket server error', error)).then(() => this.disconnect(false))
    }

    protected handleUnhandledMessage(message: unknown) {
        this.logger.warn('Received unhandled message from RPC websocket server', { message: Buffer.isBuffer(message) ? join(message) : message })
    }

    protected createClient(url: string, options: RpcWebsocketClientOptions = {}) {
        const client = new RpcWebSocketClient(url, { reconnect: true, heartbeat: true, ...options })

        client.socket.on('connected', this.handleConnected.bind(this))
        client.socket.on('disconnected', this.handleDisconnected.bind(this))
        client.socket.on('reconnect', (attempts) => this.logger.info(`Reconnecting to RPC websocket server (attempts: ${highlight(attempts)})...`))
        client.socket.on('error', this.handleError.bind(this))
        client.socket.on('message', () => client.socket.resetRetryCount())

        client.on('error', this.handleError.bind(this))
        client.on('unhandledMessage', this.handleUnhandledMessage.bind(this))
        client.on('unhandledRpcMessage', this.handleUnhandledMessage.bind(this))

        return client
    }
}
