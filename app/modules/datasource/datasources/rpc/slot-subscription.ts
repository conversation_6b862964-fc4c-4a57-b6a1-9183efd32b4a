import type { Slot } from '../../types/entities'
import { RpcSubscription } from './rpc-subscription'

export class SlotSubscription extends RpcSubscription<Slot> {
    protected readonly subscribeMethod = 'slotSubscribe'
    protected readonly subscribeParams = undefined
    protected readonly unsubscribeMethod = 'slotUnsubscribe'

    protected handleData(data: any) {
        this.emit({ slot: Number(data.slot) })
    }
}
