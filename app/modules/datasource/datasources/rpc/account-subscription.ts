import type { RpcWebSocketClient } from '@kdt310722/rpc'
import type { Logger } from '@kdt310722/logger'
import type { Account } from '../../types/entities'
import { RpcSubscription } from './rpc-subscription'

export class AccountSubscription extends RpcSubscription<Account> {
    protected readonly subscribeMethod = 'accountSubscribe'
    protected readonly unsubscribeMethod = 'accountUnsubscribe'

    public constructor(client: RpcWebSocketClient, logger: Logger, protected readonly account: string) {
        super(client, logger)
    }

    protected get subscribeParams() {
        return [this.account, { encoding: 'base64', commitment: 'confirmed' }]
    }

    protected handleData(data: any) {
        this.emit({ slot: data.context.slot, ...data.value, data: Buffer.from(data.value.data[0], 'base64') })
    }
}
