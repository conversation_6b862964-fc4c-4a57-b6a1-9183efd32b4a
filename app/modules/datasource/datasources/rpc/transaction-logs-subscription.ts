import type { RpcWebSocketClient } from '@kdt310722/rpc'
import type { Logger } from '@kdt310722/logger'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { LruMap } from '@kdt310722/utils/object'
import type { TransactionLogs } from '../../types/entities'
import { RpcSubscription } from './rpc-subscription'

export class TransactionLogsSubscription extends RpcSubscription<TransactionLogs> {
    protected readonly subscribeMethod = 'logsSubscribe'
    protected readonly unsubscribeMethod = 'logsUnsubscribe'
    protected readonly transactionIndexes = new LruMap<number, number>(1000)

    protected firstReceivedSlot?: number

    public constructor(client: RpcWebSocketClient, logger: Logger, protected readonly account: string) {
        super(client, logger)
    }

    protected get subscribeParams() {
        return [{ mentions: [this.account] }, { commitment: 'processed' }]
    }

    protected handleData(data: any) {
        const slot = Number(data.context.slot)

        if (isNullish(this.firstReceivedSlot)) {
            this.firstReceivedSlot = slot
        }

        if (slot <= this.firstReceivedSlot || notNullish(data.value.err)) {
            return
        }

        const transactionIndex = (this.transactionIndexes.get(slot) ?? -1) + 1

        this.transactionIndexes.set(slot, transactionIndex)
        this.emit({ slot, transactionIndex, signature: data.value.signature, logs: data.value.logs })
    }
}
