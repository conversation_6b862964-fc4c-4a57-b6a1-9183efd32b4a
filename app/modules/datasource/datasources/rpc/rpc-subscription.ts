import type { Rpc<PERSON>ebSocketClient } from '@kdt310722/rpc'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { isKeysOf, isObject } from '@kdt310722/utils/object'
import { type Logger, highlight, isExiting } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { Subscription } from '../../subscription'

export abstract class RpcSubscription<TData> extends Subscription<TData> {
    protected abstract readonly subscribeMethod: string
    protected abstract readonly subscribeParams: unknown
    protected abstract readonly unsubscribeMethod: string

    protected subscriptionId?: string

    public constructor(protected readonly client: RpcWebSocketClient, protected readonly logger: Logger) {
        super()

        this.client.on('notification', this.handleNotification.bind(this))
        this.client.socket.on('connected', this.handleConnected.bind(this))
        this.client.socket.on('disconnected', this.handleDisconnected.bind(this))
    }

    public async subscribe() {
        this.subscriptionId = await this.client.call(this.subscribeMethod, this.subscribeParams)
    }

    public async unsubscribe() {
        const id = tap(this.subscriptionId, () => this.subscriptionId = undefined)

        if (notNullish(id)) {
            await this.client.call(this.unsubscribeMethod, [id])
        }
    }

    protected abstract handleData(data: any): void

    protected handleConnected() {
        if (notNullish(this.subscriptionId)) {
            const method = highlight(this.subscribeMethod)
            const params = notNullish(this.subscribeParams) ? ` (${highlight(JSON.stringify(this.subscribeParams))})` : ''
            const timer = tap(this.logger.createTimer(), () => this.logger.info(`Resubscribing to event ${method}${params}...`))

            this.subscribe().then(() => this.logger.stopTimer(timer, 'info', `Resubscribed to event ${method}`)).catch((error) => {
                this.logger.stopTimer(timer, 'error', `Failed to resubscribe to event: ${method}`, error)
                this.client.socket.disconnect(false).then(() => void 0)
            })
        }
    }

    protected handleDisconnected(_: number, __: Buffer, isExplicitlyClosed: boolean) {
        if (isExplicitlyClosed) {
            this.subscriptionId = undefined
        }
    }

    protected handleNotification(_: string, params?: unknown) {
        if (isNullish(this.subscriptionId) || isExiting()) {
            return
        }

        if (notNullish(params) && isObject(params) && isKeysOf(params, ['subscription', 'result']) && params.subscription === this.subscriptionId) {
            this.handleData(params.result)
        }
    }
}
