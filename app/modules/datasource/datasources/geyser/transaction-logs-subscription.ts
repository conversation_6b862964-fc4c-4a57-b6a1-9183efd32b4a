import type { GeyserClient, GeyserSubscribeUpdate } from '@kdt-sol/geyser-client'
import { notNullish } from '@kdt310722/utils/common'
import base58 from 'bs58'
import { Subscription } from '../../subscription'
import type { TransactionLogs } from '../../types/entities'

export class TransactionLogsSubscription extends Subscription<TransactionLogs> {
    protected transactionsSubscriptionId?: string

    public constructor(protected readonly client: GeyserClient, protected readonly account: string) {
        super()

        client.on('data', (subscriptionId, data) => {
            this.handleData(subscriptionId, data)
        })
    }

    public async subscribe() {
        this.transactionsSubscriptionId = await this.client.subscribe('transactions', { vote: false, failed: false, accountRequired: [this.account], accountInclude: [], accountExclude: [] })
    }

    public async unsubscribe() {
        if (this.transactionsSubscriptionId) {
            await this.client.unsubscribe(this.transactionsSubscriptionId)
        }
    }

    protected handleData(subscriptionId: string, data: GeyserSubscribeUpdate) {
        const slot = data.transaction?.slot
        const signature = data.transaction?.transaction?.signature
        const logs = data.transaction?.transaction?.meta?.logMessages
        const transactionIndex = data.transaction?.transaction?.index

        if (subscriptionId === this.transactionsSubscriptionId && notNullish(slot) && notNullish(signature) && notNullish(logs) && notNullish(transactionIndex)) {
            this.emit({ slot: Number(slot), signature: base58.encode(signature), transactionIndex: Number(transactionIndex), logs })
        }
    }
}
