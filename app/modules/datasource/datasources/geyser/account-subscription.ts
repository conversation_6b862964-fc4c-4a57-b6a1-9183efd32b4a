import type { GeyserClient, GeyserSubscribeUpdate } from '@kdt-sol/geyser-client'
import { notNullish } from '@kdt310722/utils/common'
import base58 from 'bs58'
import { Subscription } from '../../subscription'
import type { Account } from '../../types/entities'

export class AccountSubscription extends Subscription<Account> {
    protected accountSubscriptionId?: string

    public constructor(protected readonly client: GeyserClient, protected readonly account: string) {
        super()

        client.on('data', (subscriptionId, data) => {
            this.handleData(subscriptionId, data)
        })
    }

    public async subscribe() {
        this.accountSubscriptionId = await this.client.subscribe('accounts', { account: [this.account], owner: [], filters: [] })
    }

    public async unsubscribe() {
        if (this.accountSubscriptionId) {
            await this.client.unsubscribe(this.accountSubscriptionId)
        }
    }

    protected handleData(subscriptionId: string, data: GeyserSubscribeUpdate) {
        const slot = data.account?.slot
        const account = data.account?.account

        if (subscriptionId === this.accountSubscriptionId && notNullish(slot) && notNullish(account)) {
            const pubkey = base58.encode(account.pubkey)

            if (pubkey === this.account) {
                this.emit({ slot: Number(slot), owner: base58.encode(account.owner), rentEpoch: Number(account.rentEpoch), executable: account.executable, lamports: Number(account.lamports), space: 0, data: Buffer.from(account.data) })
            }
        }
    }
}
