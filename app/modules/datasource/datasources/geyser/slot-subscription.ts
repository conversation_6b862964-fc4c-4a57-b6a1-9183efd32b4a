import type { GeyserClient, GeyserSubscribeUpdate } from '@kdt-sol/geyser-client'
import { notNullish } from '@kdt310722/utils/common'
import type { Slot } from '../../types/entities'
import { Subscription } from '../../subscription'

export class SlotSubscription extends Subscription<Slot> {
    protected blockMetaSubscriptionId?: string

    public constructor(protected readonly client: GeyserClient) {
        super()

        client.on('data', (subscriptionId, data) => {
            this.handleData(subscriptionId, data)
        })
    }

    public async subscribe() {
        this.blockMetaSubscriptionId = await this.client.subscribe('blocksMeta', {})
    }

    public async unsubscribe() {
        if (this.blockMetaSubscriptionId) {
            await this.client.unsubscribe(this.blockMetaSubscriptionId)
        }
    }

    protected handleData(subscriptionId: string, data: GeyserSubscribeUpdate) {
        if (subscriptionId === this.blockMetaSubscriptionId && notNullish(data.blockMeta) && notNullish(data.blockMeta.blockTime)) {
            this.emit({ slot: Number(data.blockMeta.slot), timestamp: Number(data.blockMeta.blockTime.timestamp) })
        }
    }
}
