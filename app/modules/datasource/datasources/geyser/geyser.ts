import { Log<PERSON>evel, type Logger, highlight } from '@kdt310722/logger'
import type { GeyserDataContext, GeyserSubscribeUpdate } from '@kdt-sol/geyser-client'
import { GeyserClient } from '@kdt-sol/geyser-client'
import { notNullish } from '@kdt310722/utils/common'
import { Datasource } from '../../datasource'
import type { GeyserClientConfig } from '../../../../config/geyser'
import { createChildLogger } from '../../../../common/logger'
import { SlotSubscription } from './slot-subscription'
import { TransactionLogsSubscription } from './transaction-logs-subscription'
import { AccountSubscription } from './account-subscription'

export class GeyserDatasource extends Datasource {
    protected readonly logger: Logger
    protected readonly client: GeyserClient

    public constructor(config: GeyserClientConfig) {
        super()

        this.logger = createChildLogger('datasources:geyser')
        this.client = this.createClient(config)
    }

    public get isConnected() {
        return this.client.isConnected
    }

    public async initialize() {
        const stop = this.logger.createLoading().start('Connecting to geyser server...')

        await this.client.connect().then(() => {
            stop('Connected to geyser server!')
        })
    }

    public async disconnect(isExplicitly = true) {
        return this.client.disconnect(isExplicitly)
    }

    public createSlotSubscription() {
        return new SlotSubscription(this.client)
    }

    public createTransactionLogsSubscription(account: string) {
        return new TransactionLogsSubscription(this.client, account)
    }

    public createAccountSubscription(account: string) {
        return new AccountSubscription(this.client, account)
    }

    protected handlePing(id: number) {
        this.logger.debug(`Sending ping request ${highlight(`#${id}`)} to geyser server...`)
    }

    protected handlePong(id: number) {
        this.logger.debug(`Received pong for ping request ${highlight(`#${id}`)} from geyser server`)
    }

    protected handleError(error: unknown) {
        if (error instanceof Error) {
            this.logger.error(`Geyser client error: ${highlight(error.message)}`)
        } else {
            this.logger.error('Geyser client error', error)
        }
    }

    protected handleData(_: string, { blockMeta, createdAt }: GeyserSubscribeUpdate, { receivedAt }: GeyserDataContext) {
        if (notNullish(blockMeta) && notNullish(createdAt)) {
            this.addLatency(BigInt(Math.max(0, (receivedAt - createdAt.getTime()) * 1e6)))
        }
    }

    protected createClient({ url, ...options }: GeyserClientConfig) {
        const client = new GeyserClient(url, options)

        client.on('error', this.handleError.bind(this))
        client.on('reconnect', (attempt, retriesLeft) => this.logger.info(`Reconnecting to geyser server (attempts: ${highlight(attempt)}, retries left: ${highlight(retriesLeft)})...`))
        client.on('reconnectFailed', (error) => this.logger.exit(1, 'fatal', 'Failed to reconnect to geyser server', error))
        client.on('disconnected', (isExplicitly) => this.logger.log(isExplicitly ? LogLevel.INFO : LogLevel.WARN, 'Disconnected from geyser server!'))
        client.on('resubscribe', (subscriptions) => this.logger.info(`Resubscribing to ${highlight(Object.keys(subscriptions).length)} geyser subscriptions...\n  + ${(Object.values(subscriptions).map((i) => highlight(`${i.method}: ${JSON.stringify(i.params)}`)).join('\n  + '))}`))
        client.on('resubscribed', (subscriptions) => this.logger.info(`Resubscribed to ${highlight(Object.keys(subscriptions).length)} geyser subscriptions!`))
        client.on('unhandledMessage', (message) => this.logger.warn('Unhandled geyser message', message))
        client.on('updated', (request) => this.logger.debug('Geyser stream updated', request))
        client.on('ping', this.handlePing.bind(this))
        client.on('pong', this.handlePong.bind(this))
        client.on('data', this.handleData.bind(this))

        client.on('reconnectError', (error) => {
            const message = 'Error while reconnecting to geyser server'

            if (error instanceof Error) {
                this.logger.warn(`${message}: ${highlight(error.message)}`)
            } else {
                this.logger.warn(message, error)
            }
        })

        return client
    }
}
