import { Emitter } from '@kdt310722/utils/event'
import type { DatasourceSubscription } from './types/subscription'
import type { Account, Slot, TransactionLogs } from './types/entities'

export type DatasourceEvents = {
    latency: (latency: bigint) => void
}

export abstract class Datasource extends Emitter<DatasourceEvents, true> {
    public abstract readonly isConnected: boolean

    protected readonly latencies: bigint[] = []

    public abstract initialize(): Promise<void>

    public abstract disconnect(isExplicitly?: boolean): Promise<void>

    public abstract createSlotSubscription(): DatasourceSubscription<Slot>

    public abstract createTransactionLogsSubscription(account: string): DatasourceSubscription<TransactionLogs>

    public abstract createAccountSubscription(account: string): DatasourceSubscription<Account>

    public getCurrentLatency() {
        return this.latencies.at(-1)
    }

    public getAverageLatency() {
        const samples = BigInt(this.latencies.length)

        if (samples === 0n) {
            return null
        }

        return this.latencies.reduce((acc, i) => acc + i, 0n) / samples
    }

    public addLatency(latency: bigint) {
        this.latencies.push(latency)
        this.emit('latency', latency)
    }
}
