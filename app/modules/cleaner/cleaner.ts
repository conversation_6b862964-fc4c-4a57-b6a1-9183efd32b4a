import { LessThan, type Repository } from 'typeorm'
import { Emitter } from '@kdt310722/utils/event'
import { tap, transform } from '@kdt310722/utils/function'
import { addMinutes } from 'date-fns/addMinutes'
import { startOfMinute } from 'date-fns/startOfMinute'
import { subMinutes } from 'date-fns/subMinutes'
import type { Token } from '../../entities/token'
import { tokenRepository, tradeRepository } from '../../common/entities'
import type { Trade } from '../../entities/trade'
import { database } from '../../common/database'
import { config } from '../../config'

export type CleanerEvents = {
    startTime: (time: Date) => void
    cleanTokens: (time: Date, cleanCompleted: boolean) => void
    cleanedTokens: (total: number, took: bigint) => void
    cleanTrades: () => void
    cleanedTrades: (total: number, took: bigint) => void
    error: (error: unknown) => void
}

export interface CleanerConfig {
    maxTokenAge: number
    cleanCompleted: boolean
    immediate: boolean
    interval: number
}

export class Cleaner extends Emitter<CleanerEvents, true> {
    protected readonly tokenRepository: Repository<Token>
    protected readonly tradeRepository: Repository<Trade>

    public constructor(protected readonly config: CleanerConfig) {
        super()

        this.tokenRepository = tokenRepository
        this.tradeRepository = tradeRepository
    }

    public async start() {
        if (this.config.immediate) {
            await this.clean()
        }

        const startTime = transform(tap(this.getIntervalStartTime(), (time) => this.emit('startTime', time)), (time) => time.getTime())
        const now = Date.now()

        setTimeout(() => this.startInterval(), Math.max(startTime - now))
    }

    protected startInterval() {
        setInterval(() => this.clean(), this.config.interval * 60 * 1000)
        Promise.resolve().then(() => this.clean())
    }

    protected getIntervalStartTime() {
        return transform(new Date(), (now) => startOfMinute(addMinutes(subMinutes(now, now.getMinutes() % this.config.interval), this.config.interval)))
    }

    protected async clean() {
        database.setOptions({ maxQueryExecutionTime: undefined })

        const isSuccess = await this.cleanTokens(this.getCleanTime(), this.config.cleanCompleted).then(() => true).catch((error) => tap(false, () => {
            this.emit('error', error)
        }))

        if (isSuccess) {
            await this.cleanTrades().catch((error) => this.emit('error', error))
        }

        database.setOptions({ maxQueryExecutionTime: config.database.maxQueryExecutionTime })
    }

    protected getCleanTime() {
        return new Date(Date.now() - this.config.maxTokenAge * 60 * 60 * 1000)
    }

    protected async cleanTrades() {
        const start = tap(process.hrtime.bigint(), () => this.emit('cleanTrades'))
        const tokensQuery = tokenRepository.createQueryBuilder('token').select('token.mint', 'mint').getQuery()
        const tradesQuery = this.tradeRepository.createQueryBuilder().delete().where(`"trades"."mint" NOT IN (${tokensQuery})`)

        await tradesQuery.execute().then((result) => {
            this.emit('cleanedTrades', result.affected ?? 0, process.hrtime.bigint() - start)
        })
    }

    protected async cleanTokens(time: Date, cleanCompleted = true) {
        const start = tap(process.hrtime.bigint(), () => this.emit('cleanTokens', time, cleanCompleted))
        let total = 0

        if (cleanCompleted) {
            total += await this.tokenRepository.delete({ isCompleted: true }).then((result) => result.affected ?? 0)
        }

        await this.tokenRepository.delete({ createdAt: LessThan(time) }).then((result) => total += (result.affected ?? 0)).then((total) => {
            this.emit('cleanedTokens', total, process.hrtime.bigint() - start)
        })
    }
}
