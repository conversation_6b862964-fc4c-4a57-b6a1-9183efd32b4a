import { type Logger, highlight } from '@kdt310722/logger'
import { tap, transform } from '@kdt310722/utils/function'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { format } from '@kdt310722/utils/number'
import { chunk, last } from '@kdt310722/utils/array'
import { type CompleteEvent, type CreateEvent, PumpEvent, type TradeEvent, parseLogs } from '@kdt-sol/pumpfun-sdk'
import { In } from 'typeorm'
import { Emitter } from '@kdt310722/utils/event'
import type { Gap } from '../../entities/gap'
import { createChildLogger } from '../../common/logger'
import { getSlots } from '../../common/rpc-client'
import { config } from '../../config'
import { gapRepository } from '../../common/gap'
import type { Token } from '../../entities/token'
import { toTokenEntity } from '../../utils/formatters/to-token-entity'
import type { EventContext } from '../indexer/pumpfun'
import type { Trade } from '../../entities/trade'
import { toTradeEntity } from '../../utils/formatters/to-trade-entity'
import { batchSaveTokens, batchSaveTrades, tokenRepository } from '../../common/entities'
import { isParseLogsExpectedError } from '../../utils/errors'
import { type TransactionLogsWithTimestamp, getTransactionsForMultipleSlots } from './transactions'

interface Stats {
    transactions: number
    tokens: number
    trades: number
}

export interface GapFillOptions {
    slotsChunkSize?: number
    transactionsChunkSize?: number
}

export type GapFillEvents = {
    tokens: (tokens: Token[]) => void
    trades: (trades: Trade[]) => void
}

export class GapFill extends Emitter<GapFillEvents, true> {
    protected readonly logger: Logger
    protected readonly slotsChunkSize: number
    protected readonly transactionsChunkSize: number

    public constructor({ slotsChunkSize = config.rpcClient.maxRequestPerSecond, transactionsChunkSize = 1000 }: GapFillOptions = {}) {
        super()

        this.logger = createChildLogger('gap-fill')
        this.slotsChunkSize = slotsChunkSize
        this.transactionsChunkSize = transactionsChunkSize
    }

    public async fill(gap: Gap) {
        const startSlot = gap.filledSlot ?? gap.startSlot
        const timer = this.getLoggerTimer(startSlot, gap.endSlot)

        if (isNullish(gap.slots) || gap.slots.length === 0) {
            gap.slots = await this.getSlots(startSlot, gap.endSlot)
        }

        gap.slots = gap.slots.filter((slot) => {
            return slot >= startSlot
        })

        this.logger.info(`Total slots to fill: ${highlight(format(gap.slots.length))}`)

        const chunks = chunk(gap.slots.toSorted((a, b) => a - b), this.slotsChunkSize)
        const stats: Stats = { transactions: 0, tokens: 0, trades: 0 }

        let chunksCount = 0

        for (const slots of chunks) {
            const { transactions, tokens, trades } = await this.fillChunk(gap, slots)

            stats.transactions += transactions
            stats.tokens += tokens
            stats.trades += trades

            chunksCount++
        }

        if (chunksCount > 1) {
            this.stopLoggerTimer(timer, gap.startSlot, gap.endSlot, stats)
        }
    }

    protected async fillChunk(gap: Gap, slots: number[]): Promise<Stats> {
        const endSlot = last(slots)
        const timer = tap(this.logger.createTimer(), () => this.logger.info(`Getting transactions from ${highlight(format(slots.length))} slots (${highlight(slots[0])} - ${highlight(endSlot)})...`))
        const transactionLogs = await getTransactionsForMultipleSlots(slots)

        this.logger.info(`Total transactions to fill: ${highlight(format(transactionLogs.length))}`)

        let tokens = 0
        let trades = 0

        for (const transactions of chunk(transactionLogs, this.transactionsChunkSize)) {
            const chunkTimer = tap(this.logger.createTimer(), () => this.logger.info(`Filling ${highlight(format(transactions.length))} transactions...`))
            const chunkStats = await this.fillTransactions(transactions)

            tokens += chunkStats.tokens
            trades += chunkStats.trades

            this.logger.stopTimer(chunkTimer, 'info', `Saved ${highlight(format(chunkStats.tokens))} tokens and ${highlight(format(chunkStats.trades))} trades!`)
        }

        await transform(gap.filledSlot = endSlot, () => {
            return gapRepository.save(gap)
        })

        return tap({ transactions: transactionLogs.length, tokens, trades }, (stats) => this.stopLoggerTimer(timer, slots[0], endSlot, stats))
    }

    protected async fillTransactions(transactions: TransactionLogsWithTimestamp[]): Promise<Omit<Stats, 'transactions'>> {
        const tokens: Record<string, Token> = {}
        const completedTokens: string[] = []
        const trades: Trade[] = []

        interface Context extends Omit<EventContext, 'timestamp'> {
            timestamp: number
        }

        const handlers = {
            [PumpEvent.CREATE]: (data: CreateEvent, context: Context) => tokens[data.mint] = toTokenEntity({ ...data, ...context }),
            [PumpEvent.TRADE]: (data: TradeEvent, context: Context) => trades.push(toTradeEntity({ ...data, ...context })),
            [PumpEvent.COMPLETE]: (data: CompleteEvent) => {
                if (notNullish(tokens[data.mint])) {
                    tokens[data.mint].isCompleted = true
                }

                completedTokens.push(data.mint)
            },
        }

        for (const { slot, signature, transactionIndex, timestamp, logs } of transactions) {
            try {
                const events = parseLogs(logs).toArray()

                for (const [eventIndex, event] of events.entries()) {
                    handlers[event.eventType]?.(event.data, { slot, signature, transactionIndex, timestamp, eventIndex })
                }
            } catch (error) {
                if (isParseLogsExpectedError(error)) {
                    this.logger.error(`Missing event identifier for logs from signature: ${signature}`)
                } else {
                    throw new Error(`Failed to parse log from signature: ${signature}`, { cause: error })
                }
            }
        }

        const insertedTokens = await batchSaveTokens(Object.values(tokens))
        const insertedTrades = await batchSaveTrades(trades)

        if (completedTokens.length > 0) {
            await tokenRepository.update({ mint: In(completedTokens) }, { isCompleted: true })
        }

        this.emit('tokens', insertedTokens)
        this.emit('trades', insertedTrades)

        return { tokens: insertedTokens.length, trades: insertedTrades.length }
    }

    protected async getSlots(startSlot: number, endSlot: number) {
        return tap(getSlots(startSlot, endSlot), () => this.logger.info('Getting slots...'))
    }

    protected getLoggerTimer(startSlot: number, endSlot: number) {
        return tap(this.logger.createTimer(), () => this.logger.info(`Start filling gap from slot ${highlight(startSlot)} to ${highlight(endSlot)}...`))
    }

    protected stopLoggerTimer(timer: string, startSlot: number, endSlot: number, stats: Stats) {
        this.logger.stopTimer(timer, 'info', `Filled gap from slot ${highlight(startSlot)} to ${highlight(endSlot)} (${Object.entries(stats).map(([key, value]) => `${key}: ${highlight(format(value))}`).join(', ')})`)
    }
}
