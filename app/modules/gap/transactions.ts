import { PUMP_PROGRAM_ADDRESS } from '@kdt-sol/pumpfun-sdk'
import { highlight } from '@kdt310722/logger'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { createChildLogger } from '../../common/logger'
import { backupRpcClient, rpcClient } from '../../common/rpc-client'
import { config } from '../../config'
import type { TransactionLogs } from '../datasource/types/entities'

export interface TransactionLogsWithTimestamp extends TransactionLogs {
    timestamp: number
}

export async function getTransactionsForMultipleSlots(slots: number[]) {
    return Promise.all(slots.map((slot) => getTransactions(slot))).then((result) => result.flat())
}

const logger = createChildLogger('modules:gap:transactions')

export async function getTransactions(slot: number, client = rpcClient, shouldRetry = true) {
    const block = await client.getBlock(BigInt(slot), { commitment: 'finalized', encoding: 'json', transactionDetails: 'full', maxSupportedTransactionVersion: 0, rewards: false }).send()
    const result: TransactionLogsWithTimestamp[] = []

    if (isNullish(block)) {
        if (notNullish(backupRpcClient) && shouldRetry) {
            return Promise.resolve().then(() => logger.warn(`Failed to get block at slot ${highlight(slot)}, retrying with backup rpc client...`)).then(() => getTransactions(slot, backupRpcClient, false))
        }

        if (config.ignoreBlockNotFound) {
            return tap([], () => logger.warn(`Block not found: ${highlight(slot)}`))
        }

        throw new Error(`Block not found: ${slot}`)
    }

    if (isNullish(block.blockTime)) {
        throw new Error(`Block time not found: ${slot}`)
    }

    const timestamp = Number(block.blockTime)

    for (const [transactionIndex, tx] of block.transactions.entries()) {
        if (isNullish(tx.meta) || notNullish(tx.meta.err) || !tx.meta.logMessages?.length) {
            continue
        }

        const logs = tx.meta.logMessages as string[]

        if (!logs.some((i) => i.includes(PUMP_PROGRAM_ADDRESS))) {
            continue
        }

        result.push({ slot, signature: tx.transaction.signatures[0], transactionIndex, logs, timestamp })
    }

    return result
}
