import { LruMap } from '@kdt310722/utils/object'
import { Emitter } from '@kdt310722/utils/event'
import { type DeferredPromise, createDeferred, withTimeout } from '@kdt310722/utils/promise'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { formatNanoseconds } from '@kdt310722/utils/number'
import type { Datasource } from '../datasource/datasource'
import type { DatasourceSubscription } from '../datasource/types/subscription'
import type { Slot } from '../datasource/types/entities'
import { Health } from './health'

export interface SlotIndexerOptions {
    maxSlots?: number
    maxTimeBetweenSlots?: number
}

export type SlotIndexerEvents = {
    slot: (slot: Slot) => void
    gap: (start: number, end: number) => void
    waiterRemoved: (slot: number) => void
    unhealthy: (reason: string) => void
}

export class SlotIndexer extends Emitter<SlotIndexerEvents, true> {
    public latestSlot?: Slot

    protected readonly subscription: DatasourceSubscription<Slot>
    protected readonly slots: LruMap<number, Slot>
    protected readonly waitingSlots: Record<number, DeferredPromise<Slot>> = {}
    protected readonly slotWaiters: Record<number, number> = {}
    protected readonly maxTimeBetweenSlots: number
    protected readonly health: Health

    public constructor(protected readonly datasource: Datasource, { maxSlots = 1000, maxTimeBetweenSlots = 5000 }: SlotIndexerOptions = {}) {
        super()

        this.subscription = datasource.createSlotSubscription()
        this.slots = new LruMap(maxSlots)
        this.maxTimeBetweenSlots = maxTimeBetweenSlots
        this.health = new Health(maxTimeBetweenSlots)
        this.health.on('unhealthy', this.handleUnhealthy.bind(this))
    }

    public addSlot(slot: Slot) {
        this.handleSlot(slot)
    }

    public async start() {
        await Promise.resolve(this.subscription.onData(this.handleSlot.bind(this))).then(async () => this.subscription.subscribe()).then(() => {
            this.health.run()
        })
    }

    public async waitForSlot(slot: number, maxWaitTime = 10_000) {
        const currentSlot = this.slots.get(slot)

        if (notNullish(currentSlot)) {
            return currentSlot
        }

        this.slotWaiters[slot] = (this.slotWaiters[slot] ?? 0) + 1

        return withTimeout(this.waitingSlots[slot] ??= createDeferred(), maxWaitTime, 'Slot wait timeout').finally(() => {
            if (--this.slotWaiters[slot] === 0) {
                delete this.slotWaiters[slot]
                delete this.waitingSlots[slot]

                this.emit('waiterRemoved', slot)
            }
        })
    }

    protected handleSlot(slot: Slot) {
        this.health.run()
        this.slots.set(slot.slot, slot)
        this.waitingSlots[slot.slot]?.resolve(slot)

        const currentSlot = this.latestSlot?.slot

        if (isNullish(this.latestSlot) || slot.slot > this.latestSlot.slot) {
            this.latestSlot = slot
            this.emit('slot', slot)
        }

        if (notNullish(currentSlot) && slot.slot - currentSlot > 1) {
            this.emit('gap', currentSlot + 1, slot.slot - 1)
        }
    }

    protected handleUnhealthy() {
        if (!this.datasource.isConnected) {
            return
        }

        Promise.resolve(this.emit('unhealthy', `No slot received after ${formatNanoseconds(BigInt(this.maxTimeBetweenSlots * 1e6))}`)).then(async () => {
            return this.datasource.disconnect(false)
        })
    }
}
