import { isExiting } from '@kdt310722/logger'
import { Emitter } from '@kdt310722/utils/event'

export type HealthEvents = {
    unhealthy: () => void
}

export class Health extends Emitter<HealthEvents, true> {
    protected timer?: NodeJS.Timeout

    public constructor(protected readonly timeout: number) {
        super()
    }

    public run() {
        clearTimeout(this.timer)

        const onTimedOut = () => {
            clearTimeout(this.timer)

            if (isExiting()) {
                return
            }

            this.emit('unhealthy')
        }

        this.timer = setTimeout(onTimedOut, this.timeout)
    }
}
