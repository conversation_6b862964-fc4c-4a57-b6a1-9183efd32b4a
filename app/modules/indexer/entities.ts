import type { CompleteEvent, CreateEvent, SetParamsEvent, TradeEvent } from '@kdt-sol/pumpfun-sdk'

export interface EventContext {
    slot: number
    signature: string
    transactionIndex: number
    eventIndex: number
}

export interface PumpFunToken extends Omit<CreateEvent, 'timestamp'>, EventContext {
    timestamp?: number
}

export interface PumpFunTrade extends Omit<TradeEvent, 'timestamp'>, EventContext {
    timestamp: number
}

export interface PumpFunCompleteEvent extends Omit<CompleteEvent, 'timestamp'>, EventContext {
    timestamp: number
}

export interface PumpFunGlobalAccountUpdateEvent extends Omit<SetParamsEvent, 'timestamp'>, EventContext {
    timestamp?: number
}
