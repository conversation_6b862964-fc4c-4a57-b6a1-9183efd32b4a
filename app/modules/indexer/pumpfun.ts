import { type CompleteEvent, type CreateEvent, PUMP_PROGRAM_ADDRESS, type ParsedLog, PumpEvent, type SetParamsEvent, type TradeEvent, parseLogs } from '@kdt-sol/pumpfun-sdk'
import { LruSet } from '@kdt310722/utils/array'
import { notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { formatNanoseconds } from '@kdt310722/utils/number'
import type { Datasource } from '../datasource/datasource'
import type { DatasourceSubscription } from '../datasource/types/subscription'
import type { TransactionLogs } from '../datasource/types/entities'
import type { EventContext as BaseEventContext, PumpFunCompleteEvent, PumpFunGlobalAccountUpdateEvent, PumpFunToken, PumpFunTrade } from './entities'
import { Health } from './health'

export interface EventContext extends BaseEventContext {
    timestamp?: number
}

export interface PumpFunIndexerOptions {
    maxCacheSize?: number
    maxTimeBetweenTrades?: number
}

export type PumpFunIndexerEvents = {
    token: (token: PumpFunToken) => void
    trade: (trade: PumpFunTrade) => void
    complete: (data: PumpFunCompleteEvent) => void
    globalAccount: (account: PumpFunGlobalAccountUpdateEvent) => void
    unhandledEvent: (event: ParsedLog, context: EventContext) => void
    unhealthy: (reason: string) => void
    error: (error: unknown) => void
    parseLogsError: (signature: string, error: unknown) => void
}

export class PumpFunIndexer extends Emitter<PumpFunIndexerEvents, true> {
    protected readonly subscription: DatasourceSubscription<TransactionLogs>
    protected readonly signatures: LruSet<string>
    protected readonly maxTimeBetweenTrades: number
    protected readonly health: Health

    public constructor(protected readonly datasource: Datasource, { maxCacheSize = 10_000, maxTimeBetweenTrades = 5000 }: PumpFunIndexerOptions = {}) {
        super()

        this.subscription = datasource.createTransactionLogsSubscription(PUMP_PROGRAM_ADDRESS)
        this.signatures = new LruSet(maxCacheSize)
        this.maxTimeBetweenTrades = maxTimeBetweenTrades
        this.health = new Health(maxTimeBetweenTrades)
        this.health.on('unhealthy', this.handleUnhealthy.bind(this))
    }

    public async start() {
        await Promise.resolve(this.subscription.onData(this.handleTransactionLogs.bind(this))).then(async () => this.subscription.subscribe()).then(() => {
            this.health.run()
        })
    }

    protected handleToken(event: CreateEvent, context: EventContext) {
        this.emit('token', { ...event, ...context, timestamp: Number(event.timestamp) })
    }

    protected handleTrade(event: TradeEvent, context: EventContext) {
        this.emit('trade', { ...event, ...context, timestamp: Number(event.timestamp) })
    }

    protected handleComplete(event: CompleteEvent, context: EventContext) {
        this.emit('complete', { ...event, ...context, timestamp: Number(event.timestamp) })
    }

    protected handleGlobalAccountUpdate(event: SetParamsEvent, context: EventContext) {
        this.emit('globalAccount', { ...event, ...context, timestamp: Number(event.timestamp) })
    }

    protected handleTransactionLogs({ slot, signature, transactionIndex, logs }: TransactionLogs) {
        if (this.signatures.has(signature)) {
            return
        }

        this.signatures.add(signature)
        this.health.run()

        try {
            const events = parseLogs(logs, false).toArray()
            const trade = events.find((e) => e.eventType === PumpEvent.TRADE)
            const timestamp = notNullish(trade) ? Number(trade.data.timestamp) : undefined

            for (const [eventIndex, event] of events.entries()) {
                this.handleEvent(event, { slot, signature, transactionIndex, timestamp, eventIndex })
            }
        } catch (error) {
            this.emit('parseLogsError', signature, error)
        }
    }

    protected handleEvent(event: ParsedLog, context: EventContext) {
        switch (event.eventType) {
            case PumpEvent.CREATE:
                this.handleToken(event.data, context)
                break
            case PumpEvent.TRADE:
                this.handleTrade(event.data, context)
                break
            case PumpEvent.COMPLETE:
                this.handleComplete(event.data, context)
                break
            case PumpEvent.SET_PARAMS:
                this.handleGlobalAccountUpdate(event.data, context)
                break
            case PumpEvent.COLLECT_CREATOR_FEE:
            case PumpEvent.COMPLETE_AMM_MIGRATION:
            case PumpEvent.EXTEND_ACCOUNT:
            case PumpEvent.SET_CREATOR:
            case PumpEvent.SET_METAPLEX_CREATOR:
            case PumpEvent.UPDATE_GLOBAL_AUTHORITY:
            case PumpEvent.ADMIN_SET_CREATOR:
            case PumpEvent.ADMIN_SET_IDL_AUTHORITY:
            case PumpEvent.ADMIN_UPDATE_TOKEN_INCENTIVES:
            case PumpEvent.CLAIM_TOKEN_INCENTIVES:
            case PumpEvent.CLOSE_USER_VOLUME_ACCUMULATOR:
            case PumpEvent.INIT_USER_VOLUME_ACCUMULATOR:
            case PumpEvent.SYNC_USER_VOLUME_ACCUMULATOR:
                break
            default:
                this.emit('unhandledEvent', event, context)
                break
        }
    }

    protected handleUnhealthy() {
        if (!this.datasource.isConnected) {
            return
        }

        Promise.resolve(this.emit('unhealthy', `No trades received after ${formatNanoseconds(BigInt(this.maxTimeBetweenTrades * 1e6))}`)).then(async () => {
            return this.datasource.disconnect(false)
        })
    }
}
