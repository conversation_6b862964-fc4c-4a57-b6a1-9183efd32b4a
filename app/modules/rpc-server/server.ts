import type { AnyObject } from '@kdt310722/utils/object'
import type { Logger } from '@kdt310722/logger'
import { createChildLogger } from '../../common/logger'
import { HttpRpcServer, type HttpRpcServerOptions } from './http'
import { WebsocketRpcServer, type WebsocketRpcServerOptions } from './websocket'

export type RpcServerOptions<TContext extends AnyObject = AnyObject> = HttpRpcServerOptions<TContext> & Pick<WebsocketRpcServerOptions<TContext>, 'heartbeat'>

export class RpcServer<TContext extends AnyObject = AnyObject> {
    public readonly logger: Logger
    public readonly http: HttpRpcServer<TContext>
    public readonly ws: WebsocketRpcServer<TContext>

    public constructor(public readonly host: string, public readonly port: number, options: RpcServerOptions<TContext> = {}) {
        this.logger = createChildLogger('rpc-server')
        this.http = new HttpRpcServer(options)
        this.ws = new WebsocketRpcServer(host, port, { ...options, listener: this.http.server })
    }

    public async start() {
        return this.ws.start()
    }
}
