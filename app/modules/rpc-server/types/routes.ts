import type { Zod<PERSON><PERSON>Any } from 'zod'
import type { AnyObject } from '@kdt310722/utils/object'
import type { RpcMethodHandler } from '@kdt310722/rpc'
import type { WebsocketRpcMethodHandler } from '../websocket'

export interface BaseRoute {
    name?: string
    schema?: ZodTypeAny
}

export interface HttpRoute<TContext extends AnyObject = AnyObject> extends BaseRoute {
    httpHandler: RpcMethodHandler<TContext>
}

export interface WebSocketRoute<TContext extends AnyObject = AnyObject> extends BaseRoute {
    wsHandler: WebsocketRpcMethodHandler<TContext>
}

export interface HttpAndWebSocketRoute<TContext extends AnyObject = AnyObject> extends BaseRoute {
    httpHandler: RpcMethodHandler<TContext>
    wsHandler: WebsocketRpcMethodHandler<TContext>
}

export interface RouteWithOneHandler<TContext extends AnyObject = AnyObject> extends BaseRoute {
    handler: RpcMeth<PERSON><PERSON>and<PERSON><TContext>
}

export type Route<TContext extends AnyObject = AnyObject> = HttpRoute<TContext> | WebSocketRoute<TContext> | HttpAndWebSocketRoute<TContext> | RouteWithOneHandler<TContext>
