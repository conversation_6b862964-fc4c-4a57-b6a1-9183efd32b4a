import { LogLevel, type Logger } from '@kdt310722/logger'
import { RpcRequestError } from '../errors/rpc-request-error'

export function handleRequestError(logger: Logger, error: unknown, request?: unknown, logLevel = LogLevel.ERROR) {
    const requestError = RpcRequestError.from(error, request)

    if (requestError.isImportant()) {
        logger.log(logLevel, 'Error while handle RPC request', requestError)
    }

    return requestError.toJsonRpcError()
}
