import { address } from '@solana/kit'

export const WSOL_USDC_PAIR_ADDRESS = address('Czfq3xZZDmsdGdUyrNLtRhGc47cXcZtLG4crryfu44zE')

export const WSOL_DECIMALS = 9

export const USDC_DECIMALS = 6

export const SORT_KEY_SLOT_SCALE = 1_000_000_000n

export const SORT_KEY_TRANSACTION_INDEX_SCALE = 1000n

export const MAX_TRANSACTION_INDEX = 9999

export const CANDLESTICK_INTERVALS = <const>{
    '1s': { name: '1 second', scheduleInterval: '5 seconds' },
    '15s': { name: '15 seconds', scheduleInterval: '30 seconds' },
    '30s': { name: '30 seconds', scheduleInterval: '1 minute' },
    '1m': { name: '1 minute', scheduleInterval: '2 minutes' },
    '5m': { name: '5 minutes', scheduleInterval: '10 minutes' },
    '15m': { name: '15 minutes', scheduleInterval: '30 minutes' },
    '30m': { name: '30 minutes', scheduleInterval: '1 hour' },
    '1h': { name: '1 hour', scheduleInterval: '2 hours' },
    '4h': { name: '4 hours', scheduleInterval: '8 hours' },
    '6h': { name: '6 hours', scheduleInterval: '12 hours' },
    '12h': { name: '12 hours', scheduleInterval: '1 day' },
}

export type Interval = keyof typeof CANDLESTICK_INTERVALS
