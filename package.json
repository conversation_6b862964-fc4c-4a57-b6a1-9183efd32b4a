hore{
    "type": "module",
    "private": true,
    "packageManager": "pnpm@10.15.1",
    "author": "<PERSON><PERSON>g <<EMAIL>>",
    "license": "UNLICENSED",
    "engines": {
        "node": ">=20.12.0"
    },
    "scripts": {
        "dev": "node bin/run.js",
        "start": "NODE_ENV=production node bin/run.js",
        "typeorm": "node bin/typeorm.js -d app/common/database.ts",
        "migration:generate": "node bin/typeorm.js -d app/common/database.ts migration:generate",
        "migration:create": "node bin/typeorm.js migration:create",
        "up": "ncu -i -x zod -x zod-validation-error -x @solana/kit",
        "lint": "eslint .",
        "lint:fix": "eslint . --fix",
        "preinstall": "npx only-allow pnpm",
        "prepare": "simple-git-hooks"
    },
    "dependencies": {
        "@kdt-sol/geyser-client": "^0.0.6",
        "@kdt-sol/pumpfun-sdk": "^0.5.0",
        "@kdt310722/config": "^0.0.4",
        "@kdt310722/logger": "^0.0.12",
        "@kdt310722/rpc": "^0.2.1",
        "@kdt310722/utils": "^0.0.19",
        "@orca-so/whirlpools-client": "^3.0.0",
        "@orca-so/whirlpools-core": "^2.0.0",
        "@solana/kit": "^2.3.0",
        "bottleneck": "^2.19.5",
        "bs58": "^6.0.0",
        "change-case": "^5.4.4",
        "cors": "^2.8.5",
        "date-fns": "^4.1.0",
        "express": "^5.1.0",
        "fast-glob": "^3.3.3",
        "helmet": "^8.1.0",
        "p-queue": "^8.1.0",
        "pg": "^8.16.3",
        "pluralize": "^8.0.0",
        "reflect-metadata": "^0.2.2",
        "typeorm": "^0.3.26",
        "typeorm-naming-strategies": "^4.1.0",
        "ws": "^8.18.3",
        "zod": "3.25.76",
        "zod-validation-error": "3.5.2"
    },
    "devDependencies": {
        "@commitlint/cli": "^19.8.1",
        "@commitlint/config-conventional": "^19.8.1",
        "@kdt310722/eslint-config": "^0.2.0",
        "@kdt310722/tsconfig": "^1.0.0",
        "@types/cors": "^2.8.19",
        "@types/express": "^5.0.3",
        "@types/node": "^24.3.0",
        "@types/pluralize": "^0.0.33",
        "@types/ws": "^8.18.1",
        "eslint": "^9.34.0",
        "lint-staged": "^16.1.6",
        "npm-check-updates": "^18.0.3",
        "only-allow": "^1.2.1",
        "simple-git-hooks": "^2.13.1",
        "ts-node-maintained": "^10.9.6",
        "typescript": "^5.9.2"
    },
    "commitlint": {
        "extends": "@commitlint/config-conventional"
    },
    "pnpm": {
        "onlyBuiltDependencies": [
            "protobufjs",
            "simple-git-hooks",
            "unrs-resolver"
        ],
        "ignoredBuiltDependencies": [
            "@kdt-sol/geyser-client",
            "@kdt-sol/pumpfun-sdk",
            "@kdt310722/config",
            "@kdt310722/eslint-config",
            "@kdt310722/logger",
            "@kdt310722/rpc",
            "@kdt310722/utils"
        ]
    },
    "simple-git-hooks": {
        "commit-msg": "npx --no -- commitlint --edit ${1}",
        "pre-commit": "npx tsc --noEmit && npx lint-staged"
    },
    "lint-staged": {
        "*": "eslint --fix"
    }
}
